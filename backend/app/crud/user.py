from typing import Any, Dict, Optional, Union, List
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_
from datetime import datetime
from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBase
from app.models.user import User, Role
from app.schemas.user import UserCreate, UserUpdate


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    def get_by_email(self, db: Session, *, email: str) -> Optional[User]:
        return db.query(User).filter(User.email == email).first()

    def get_by_username(self, db: Session, *, username: str) -> Optional[User]:
        return db.query(User).filter(User.username == username).first()

    def create(self, db: Session, *, obj_in: UserCreate, created_by: str = None) -> User:
        # Get the user ID for created_by if provided
        created_by_id = None
        if created_by:
            creator = self.get_by_username(db, username=created_by)
            if creator:
                created_by_id = creator.id

        db_obj = User(
            email=obj_in.email,
            username=obj_in.username,
            first_name=obj_in.first_name,
            last_name=obj_in.last_name,
            password=get_password_hash(obj_in.password),
            active=obj_in.active,
            is_superuser=obj_in.is_superuser,
            created_by_fk=created_by_id,
            changed_by_fk=created_by_id
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, db: Session, *, db_obj: User, obj_in: Union[UserUpdate, Dict[str, Any]], updated_by: str = None
    ) -> User:
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        
        if "password" in update_data:
            hashed_password = get_password_hash(update_data["password"])
            del update_data["password"]
            update_data["password"] = hashed_password
        
        if updated_by:
            # Get the user ID for updated_by
            updater = self.get_by_username(db, username=updated_by)
            if updater:
                update_data["changed_by_fk"] = updater.id
            
        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def get_with_roles(self, db: Session, id: int) -> Optional[User]:
        """Get user with roles loaded"""
        return db.query(User).options(
            joinedload(User.user_roles).joinedload("role")
        ).filter(User.id == id).first()

    def get_multi_with_roles(
        self, db: Session, *, skip: int = 0, limit: int = 100, search: str = None
    ) -> List[User]:
        """Get multiple users with roles loaded and optional search"""
        query = db.query(User).options(
            joinedload(User.user_roles).joinedload("role")
        )

        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_filter),
                    User.email.ilike(search_filter),
                    User.first_name.ilike(search_filter),
                    User.last_name.ilike(search_filter)
                )
            )

        return query.offset(skip).limit(limit).all()

    def authenticate(self, db: Session, *, username: str, password: str) -> Optional[User]:
        # Try to find user by username first
        user = self.get_by_username(db, username=username)

        # If not found by username, try by email
        if not user:
            user = self.get_by_email(db, email=username)

        if not user:
            return None
        if not verify_password(password, user.password):
            return None

        # Update last login and login count
        self.update_login_info(db, user=user)
        return user

    def update_login_info(self, db: Session, *, user: User) -> User:
        """Update user login information"""
        user.last_login = datetime.utcnow()
        user.login_count = (user.login_count or 0) + 1
        db.commit()
        db.refresh(user)
        return user

    def is_active(self, user: User) -> bool:
        return user.active

    def is_superuser(self, user: User) -> bool:
        return user.is_superuser


class CRUDRole(CRUDBase[Role, Any, Any]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Role]:
        return db.query(Role).filter(Role.name == name).first()


user = CRUDUser(User)
role = CRUDRole(Role)
