from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.orm import Session
from sqlalchemy import cast, String
import csv
import io
from app.api import deps
from app.core.database import get_db
from app.crud import data_input as crud_data_input, data_input_attribute as crud_data_input_attribute, record as crud_record
from app.services.brand_access import BrandAccessService
from app.services.validation import DataValidationService
from app.services.record_filter import RecordFilterService
from app.models.user import User
from app.models.data_input import Record as RecordModel
from app.schemas.data_input import (
    DataInput, DataInputCreate, DataInputUpdate,
    DataInputAttribute, DataInputAttributeCreate, DataInputAttributeUpdate,
    Record, RecordCreate, RecordUpdate, RecordValidationResponse
)
from app.schemas.record_filter import (
    RecordFilterRequest, RecordFilterResponse, FilterValidationRequest,
    FilterValidationResponse, RecordStatisticsResponse, ExportRequest, ExportResponse
)

router = APIRouter()


@router.get("/health")
def health_check() -> Any:
    """
    Health check endpoint for Kubernetes liveness and readiness probes.
    """
    return {"status": "healthy", "service": "sr-internal-service-backend"}


def check_data_input_brand_access(
    data_input_id: int,
    permission: str,
    current_user: User,
    brand_service: BrandAccessService,
    db: Session
) -> None:
    """Helper function to check brand access for data input operations"""
    if current_user.is_superuser:
        return  # Superusers bypass brand access control

    # Get the data input to find its brand
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check brand access
    brand_service.check_permission(
        user_id=current_user.id,
        brand_id=data_input.brand_id,
        permission=permission,
        raise_exception=True
    )


# Data Input endpoints
@router.get("/")
def read_data_inputs(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    brand_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve data inputs with brand information.
    """
    # For now, simplify to superuser access - can add brand filtering later
    data_inputs = crud_data_input.get_multi(db, skip=skip, limit=limit)

    # Convert to dict format with brand information and record count
    result = []
    for data_input in data_inputs:
        # Count records for this data input
        record_count = db.query(RecordModel).filter(
            RecordModel.data_input_id == data_input.id,
            RecordModel.is_deleted == False
        ).count()

        data_input_dict = {
            "id": data_input.id,
            "name": data_input.name,
            "description": data_input.description,
            "brand_id": data_input.brand_id,
            "is_active": data_input.is_active,
            "record_count": record_count,
            "created_at": data_input.created_at.isoformat() if data_input.created_at else None,
            "updated_at": data_input.updated_at.isoformat() if data_input.updated_at else None,
            "created_by": data_input.created_by,
            "updated_by": data_input.updated_by,
        }

        # Add brand information
        if data_input.brand:
            data_input_dict["brand"] = {
                "id": data_input.brand.id,
                "name": data_input.brand.name,
                "description": data_input.brand.description,
                "is_active": data_input.brand.is_active
            }
        else:
            data_input_dict["brand"] = None

        result.append(data_input_dict)

    return result


@router.post("/")
def create_data_input(
    *,
    db: Session = Depends(get_db),
    data_input_in: DataInputCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new data input.
    """
    data_input = crud_data_input.get_by_name(db, name=data_input_in.name)
    if data_input:
        raise HTTPException(
            status_code=400,
            detail="The data input with this name already exists in the system.",
        )
    data_input = crud_data_input.create(db, obj_in=data_input_in, created_by=current_user.username)

    # Return with brand information
    result = {
        "id": data_input.id,
        "name": data_input.name,
        "description": data_input.description,
        "brand_id": data_input.brand_id,
        "is_active": data_input.is_active,
        "created_at": data_input.created_at.isoformat() if data_input.created_at else None,
        "updated_at": data_input.updated_at.isoformat() if data_input.updated_at else None,
        "created_by": data_input.created_by,
        "updated_by": data_input.updated_by,
    }

    # Add brand information
    if data_input.brand:
        result["brand"] = {
            "id": data_input.brand.id,
            "name": data_input.brand.name,
            "description": data_input.brand.description,
            "is_active": data_input.brand.is_active
        }
    else:
        result["brand"] = None

    return result


@router.get("/{data_input_id}")
def read_data_input(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get data input by ID with brand information.
    """
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Return with brand information
    result = {
        "id": data_input.id,
        "name": data_input.name,
        "description": data_input.description,
        "brand_id": data_input.brand_id,
        "is_active": data_input.is_active,
        "created_at": data_input.created_at.isoformat() if data_input.created_at else None,
        "updated_at": data_input.updated_at.isoformat() if data_input.updated_at else None,
        "created_by": data_input.created_by,
        "updated_by": data_input.updated_by,
    }

    # Add brand information
    if data_input.brand:
        result["brand"] = {
            "id": data_input.brand.id,
            "name": data_input.brand.name,
            "description": data_input.brand.description,
            "is_active": data_input.brand.is_active
        }
    else:
        result["brand"] = None

    # Add attributes if they exist
    if hasattr(data_input, 'attributes') and data_input.attributes:
        result["attributes"] = []
        for attr in data_input.attributes:
            result["attributes"].append({
                "id": attr.id,
                "data_input_id": attr.data_input_id,
                "name": attr.name,
                "label": attr.label,
                "description": attr.description,
                "data_type": attr.data_type,
                "is_required": attr.is_required,
                "is_unique": attr.is_unique,
                "is_active": attr.is_active,
                "default_value": attr.default_value,
                "min_length": attr.min_length,
                "max_length": attr.max_length,
                "min_value": attr.min_value,
                "max_value": attr.max_value,
                "pattern": attr.pattern,
                "allowed_values": attr.allowed_values,
                "display_order": attr.display_order,
                "help_text": attr.help_text,
                "placeholder": attr.placeholder,
                "format": attr.format,
                "created_at": attr.created_at.isoformat() if attr.created_at else None,
                "updated_at": attr.updated_at.isoformat() if attr.updated_at else None,
                "created_by": attr.created_by,
                "updated_by": attr.updated_by,
            })
    else:
        result["attributes"] = []

    return result


@router.put("/{data_input_id}")
def update_data_input(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    data_input_in: DataInputUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a data input.
    """
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")
    data_input = crud_data_input.update(db, db_obj=data_input, obj_in=data_input_in, updated_by=current_user.username)

    # Return with brand information
    result = {
        "id": data_input.id,
        "name": data_input.name,
        "description": data_input.description,
        "brand_id": data_input.brand_id,
        "is_active": data_input.is_active,
        "created_at": data_input.created_at.isoformat() if data_input.created_at else None,
        "updated_at": data_input.updated_at.isoformat() if data_input.updated_at else None,
        "created_by": data_input.created_by,
        "updated_by": data_input.updated_by,
    }

    # Add brand information
    if data_input.brand:
        result["brand"] = {
            "id": data_input.brand.id,
            "name": data_input.brand.name,
            "description": data_input.brand.description,
            "is_active": data_input.brand.is_active
        }
    else:
        result["brand"] = None

    return result


# Data Input Attribute endpoints
@router.get("/{data_input_id}/attributes")
def read_data_input_attributes(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get attributes for a data input.
    """
    attributes = crud_data_input_attribute.get_by_data_input(db, data_input_id=data_input_id)

    # Convert to dict format for JSON serialization
    result = []
    for attr in attributes:
        attr_dict = {
            "id": attr.id,
            "data_input_id": attr.data_input_id,
            "name": attr.name,
            "label": attr.label,
            "description": attr.description,
            "data_type": attr.data_type,
            "is_required": attr.is_required,
            "is_unique": attr.is_unique,
            "is_active": attr.is_active,
            "default_value": attr.default_value,
            "min_length": attr.min_length,
            "max_length": attr.max_length,
            "min_value": attr.min_value,
            "max_value": attr.max_value,
            "pattern": attr.pattern,
            "allowed_values": attr.allowed_values,
            "display_order": attr.display_order,
            "help_text": attr.help_text,
            "placeholder": attr.placeholder,
            "created_at": attr.created_at.isoformat() if attr.created_at else None,
            "updated_at": attr.updated_at.isoformat() if attr.updated_at else None,
            "created_by": attr.created_by,
            "updated_by": attr.updated_by,
        }
        result.append(attr_dict)

    return result


@router.post("/{data_input_id}/attributes")
def create_data_input_attribute(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    attribute_in: DataInputAttributeCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new data input attribute.
    """
    # Verify data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Validate format field for Date/Datetime types
    if attribute_in.data_type in ['Date', 'Datetime']:
        if not attribute_in.format:
            raise HTTPException(
                status_code=400,
                detail=f"Format is required for {attribute_in.data_type} data type"
            )

        # Validate format string (basic validation)
        valid_formats = {
            'Date': [
                'YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY', 'DD-MM-YYYY',
                'MMM DD, YYYY', 'MMMM DD, YYYY'
            ],
            'Datetime': [
                'YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm', 'MM/DD/YYYY HH:mm:ss',
                'MM/DD/YYYY HH:mm', 'DD/MM/YYYY HH:mm:ss', 'DD/MM/YYYY HH:mm',
                'MMM DD, YYYY HH:mm', 'MMMM DD, YYYY HH:mm:ss'
            ]
        }

        if attribute_in.format not in valid_formats[attribute_in.data_type]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid format '{attribute_in.format}' for {attribute_in.data_type} data type"
            )

    # Check if attribute with same name already exists for this data input
    existing_attr = crud_data_input_attribute.get_by_name_and_data_input(
        db, name=attribute_in.name, data_input_id=data_input_id
    )
    if existing_attr:
        raise HTTPException(
            status_code=400,
            detail="Attribute with this name already exists for this data input.",
        )

    attribute_in.data_input_id = data_input_id
    attribute = crud_data_input_attribute.create(db, obj_in=attribute_in, created_by=current_user.username)

    # Return dict format for JSON serialization
    return {
        "id": attribute.id,
        "data_input_id": attribute.data_input_id,
        "name": attribute.name,
        "label": attribute.label,
        "description": attribute.description,
        "data_type": attribute.data_type,
        "is_required": attribute.is_required,
        "is_unique": attribute.is_unique,
        "is_active": attribute.is_active,
        "default_value": attribute.default_value,
        "min_length": attribute.min_length,
        "max_length": attribute.max_length,
        "min_value": attribute.min_value,
        "max_value": attribute.max_value,
        "pattern": attribute.pattern,
        "allowed_values": attribute.allowed_values,
        "display_order": attribute.display_order,
        "help_text": attribute.help_text,
        "placeholder": attribute.placeholder,
        "format": attribute.format,
        "created_at": attribute.created_at.isoformat() if attribute.created_at else None,
        "updated_at": attribute.updated_at.isoformat() if attribute.updated_at else None,
        "created_by": attribute.created_by,
        "updated_by": attribute.updated_by,
    }


@router.get("/{data_input_id}/attributes/{attribute_id}")
def read_data_input_attribute(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    attribute_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get a specific attribute by ID.
    """
    attribute = crud_data_input_attribute.get(db, id=attribute_id)
    if not attribute or attribute.data_input_id != data_input_id:
        raise HTTPException(status_code=404, detail="Attribute not found")

    # Return dict format for JSON serialization
    return {
        "id": attribute.id,
        "data_input_id": attribute.data_input_id,
        "name": attribute.name,
        "label": attribute.label,
        "description": attribute.description,
        "data_type": attribute.data_type,
        "is_required": attribute.is_required,
        "is_unique": attribute.is_unique,
        "is_active": attribute.is_active,
        "default_value": attribute.default_value,
        "min_length": attribute.min_length,
        "max_length": attribute.max_length,
        "min_value": attribute.min_value,
        "max_value": attribute.max_value,
        "pattern": attribute.pattern,
        "allowed_values": attribute.allowed_values,
        "display_order": attribute.display_order,
        "help_text": attribute.help_text,
        "placeholder": attribute.placeholder,
        "format": attribute.format,
        "created_at": attribute.created_at.isoformat() if attribute.created_at else None,
        "updated_at": attribute.updated_at.isoformat() if attribute.updated_at else None,
        "created_by": attribute.created_by,
        "updated_by": attribute.updated_by,
    }


@router.put("/{data_input_id}/attributes/{attribute_id}")
def update_data_input_attribute(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    attribute_id: int,
    attribute_in: DataInputAttributeUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a data input attribute.
    """
    attribute = crud_data_input_attribute.get(db, id=attribute_id)
    if not attribute or attribute.data_input_id != data_input_id:
        raise HTTPException(status_code=404, detail="Attribute not found")

    # Check if attribute with same name already exists for this data input (excluding current)
    if attribute_in.name and attribute_in.name != attribute.name:
        existing_attr = crud_data_input_attribute.get_by_name_and_data_input(
            db, name=attribute_in.name, data_input_id=data_input_id
        )
        if existing_attr and existing_attr.id != attribute_id:
            raise HTTPException(
                status_code=400,
                detail="Attribute with this name already exists for this data input.",
            )

    attribute = crud_data_input_attribute.update(
        db, db_obj=attribute, obj_in=attribute_in, updated_by=current_user.username
    )

    # Return dict format for JSON serialization
    return {
        "id": attribute.id,
        "data_input_id": attribute.data_input_id,
        "name": attribute.name,
        "label": attribute.label,
        "description": attribute.description,
        "data_type": attribute.data_type,
        "is_required": attribute.is_required,
        "is_unique": attribute.is_unique,
        "is_active": attribute.is_active,
        "default_value": attribute.default_value,
        "min_length": attribute.min_length,
        "max_length": attribute.max_length,
        "min_value": attribute.min_value,
        "max_value": attribute.max_value,
        "pattern": attribute.pattern,
        "allowed_values": attribute.allowed_values,
        "display_order": attribute.display_order,
        "help_text": attribute.help_text,
        "placeholder": attribute.placeholder,
        "format": attribute.format,
        "created_at": attribute.created_at.isoformat() if attribute.created_at else None,
        "updated_at": attribute.updated_at.isoformat() if attribute.updated_at else None,
        "created_by": attribute.created_by,
        "updated_by": attribute.updated_by,
    }





# Record endpoints
@router.get("/{data_input_id}/records")
def read_records(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    filter_column: Optional[str] = Query(None),
    filter_value: Optional[str] = Query(None),
    include_deleted: bool = False,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get records for a data input with pagination, search, and filtering.
    """
    # Calculate skip from page
    skip = (page - 1) * limit

    # Build query
    query = db.query(RecordModel).filter(RecordModel.data_input_id == data_input_id)
    if not include_deleted:
        query = query.filter(RecordModel.is_deleted == False)

    # Apply search filter
    if search:
        # Search across all JSON values using proper JSONB syntax
        query = query.filter(cast(RecordModel.value, String).ilike(f'%{search}%'))

    # Apply column-specific filter
    if filter_column and filter_value:
        # Filter by specific column in JSON value
        query = query.filter(RecordModel.value[filter_column].astext.ilike(f'%{filter_value}%'))

    # Get total count
    total = query.count()

    # Get records with pagination
    records = query.offset(skip).limit(limit).all()

    # Convert records to dict format for JSON serialization
    records_data = []
    for record in records:
        record_dict = {
            "id": record.id,
            "data_input_id": record.data_input_id,
            "value": record.value,
            "created_at": record.created_at.isoformat() if record.created_at else None,
            "updated_at": record.updated_at.isoformat() if record.updated_at else None,
            "created_by": record.created_by,
            "updated_by": record.updated_by,
            "is_deleted": record.is_deleted,
            "deleted_at": record.deleted_at.isoformat() if record.deleted_at else None,
            "deleted_by": record.deleted_by
        }
        records_data.append(record_dict)

    return {
        "records": records_data,
        "total": total,
        "page": page,
        "limit": limit,
        "total_pages": (total + limit - 1) // limit  # Ceiling division
    }


@router.post("/{data_input_id}/records")
def create_record(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    record_in: RecordCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new record with validation.
    """
    # Verify data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # For now, skip brand access check to simplify
    # check_data_input_brand_access(
    #     data_input_id, "create", current_user, brand_service, db
    # )

    # Skip validation for now to simplify debugging
    # if record_in.validate_data and record_in.value:
    #     validation_service = DataValidationService(db)
    #     validation_result = validation_service.validate_record_data(
    #         data_input_id=data_input_id,
    #         data=record_in.value,
    #         validate_required=True,
    #         validate_unique=True
    #     )

    #     if not validation_result.is_valid:
    #         raise HTTPException(
    #             status_code=422,
    #             detail={
    #                 "message": "Data validation failed",
    #                 "errors": [error.dict() for error in validation_result.errors],
    #                 "warnings": [warning.dict() for warning in validation_result.warnings]
    #             }
    #         )

    #     # Use validated data
    #     record_in.value = validation_result.validated_data

    # Set data_input_id and create record
    record_in.data_input_id = data_input_id

    # Create a dict without validate_data for the model
    record_data = record_in.model_dump(exclude={'validate_data'})

    # Create the record using the filtered data
    from app.models.data_input import Record as RecordModel
    db_obj = RecordModel(**record_data)
    db_obj.created_by = current_user.username
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    record = db_obj

    # Return dict format for JSON serialization
    return {
        "id": record.id,
        "data_input_id": record.data_input_id,
        "value": record.value,
        "created_at": record.created_at.isoformat() if record.created_at else None,
        "updated_at": record.updated_at.isoformat() if record.updated_at else None,
        "created_by": record.created_by,
        "updated_by": record.updated_by,
        "is_deleted": record.is_deleted,
        "deleted_at": record.deleted_at.isoformat() if record.deleted_at else None,
        "deleted_by": record.deleted_by
    }


@router.get("/{data_input_id}/records/{record_id}")
def read_record(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    record_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get a specific record by ID.
    """
    record = crud_record.get(db, id=record_id)
    if not record or record.data_input_id != data_input_id:
        raise HTTPException(status_code=404, detail="Record not found")

    # Return dict format for JSON serialization
    return {
        "id": record.id,
        "data_input_id": record.data_input_id,
        "value": record.value,
        "created_at": record.created_at.isoformat() if record.created_at else None,
        "updated_at": record.updated_at.isoformat() if record.updated_at else None,
        "created_by": record.created_by,
        "updated_by": record.updated_by,
        "is_deleted": record.is_deleted,
        "deleted_at": record.deleted_at.isoformat() if record.deleted_at else None,
        "deleted_by": record.deleted_by
    }


@router.put("/{data_input_id}/records/{record_id}")
def update_record(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    record_id: int,
    record_in: RecordUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a record with validation.
    """
    record = crud_record.get(db, id=record_id)
    if not record or record.data_input_id != data_input_id:
        raise HTTPException(status_code=404, detail="Record not found")

    # For now, skip brand access check to simplify
    # check_data_input_brand_access(
    #     record.data_input_id, "edit", current_user, brand_service, db
    # )

    # Skip validation for now to simplify debugging
    # if record_in.validate_data and record_in.value:
    #     validation_service = DataValidationService(db)
    #     validation_result = validation_service.validate_record_data(
    #         data_input_id=record.data_input_id,
    #         data=record_in.value,
    #         validate_required=True,
    #         validate_unique=True,
    #         existing_record_id=record_id
    #     )

    #     if not validation_result.is_valid:
    #         raise HTTPException(
    #             status_code=422,
    #             detail={
    #                 "message": "Data validation failed",
    #                 "errors": [error.model_dump() for error in validation_result.errors],
    #                 "warnings": [warning.model_dump() for warning in validation_result.warnings]
    #             }
    #         )

    #     # Use validated data
    #     record_in.value = validation_result.validated_data

    record = crud_record.update_record(
        db, record_id=record_id, obj_in=record_in, updated_by=current_user.username
    )

    # Return dict format for JSON serialization
    return {
        "id": record.id,
        "data_input_id": record.data_input_id,
        "value": record.value,
        "created_at": record.created_at.isoformat() if record.created_at else None,
        "updated_at": record.updated_at.isoformat() if record.updated_at else None,
        "created_by": record.created_by,
        "updated_by": record.updated_by,
        "is_deleted": record.is_deleted,
        "deleted_at": record.deleted_at.isoformat() if record.deleted_at else None,
        "deleted_by": record.deleted_by
    }


@router.delete("/{data_input_id}/records/{record_id}")
def delete_record(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    record_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Soft delete a record.
    """
    record = crud_record.get(db, id=record_id)
    if not record or record.data_input_id != data_input_id:
        raise HTTPException(status_code=404, detail="Record not found")

    record = crud_record.soft_delete(db, id=record_id, deleted_by=current_user.username)
    return {"message": "Record deleted successfully"}


@router.post("/{data_input_id}/records/bulk-upload")
def bulk_upload_records(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Bulk upload records from CSV file.

    CSV format requirements:
    - First row must contain column headers
    - Column headers should match data input attribute names
    - Data will be validated according to attribute definitions
    """
    # Verify data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check file type
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV file")

    try:
        # Read CSV content
        content = file.file.read()
        csv_content = content.decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_content))

        # Get data input attributes for validation
        attributes = crud_data_input_attribute.get_by_data_input(db, data_input_id=data_input_id)
        attribute_map = {attr.name: attr for attr in attributes}

        # Process records
        created_records = []
        errors = []
        row_number = 1  # Start from 1 (header is row 0)

        for row in csv_reader:
            row_number += 1
            try:
                # Build record value from CSV row
                record_value = {}

                for column_name, cell_value in row.items():
                    if column_name and column_name.strip():  # Skip empty column names
                        column_name = column_name.strip()

                        # Check if column matches an attribute
                        if column_name in attribute_map:
                            attribute = attribute_map[column_name]

                            # Convert value based on data type
                            if cell_value and str(cell_value).strip():
                                try:
                                    converted_value = convert_csv_value(cell_value, attribute.data_type)
                                    record_value[column_name] = converted_value
                                except ValueError as e:
                                    errors.append({
                                        "row": row_number,
                                        "column": column_name,
                                        "value": cell_value,
                                        "error": f"Invalid {attribute.data_type} value: {str(e)}"
                                    })
                                    continue
                            elif attribute.is_required:
                                errors.append({
                                    "row": row_number,
                                    "column": column_name,
                                    "value": cell_value,
                                    "error": "Required field cannot be empty"
                                })
                                continue

                # Create record if no errors for this row
                if not any(error["row"] == row_number for error in errors):
                    # Create record
                    record_data = {
                        "data_input_id": data_input_id,
                        "value": record_value
                    }

                    db_obj = RecordModel(**record_data)
                    db_obj.created_by = current_user.username
                    db.add(db_obj)
                    db.flush()  # Get the ID without committing

                    created_records.append({
                        "id": db_obj.id,
                        "row": row_number,
                        "data": record_value
                    })

            except Exception as e:
                errors.append({
                    "row": row_number,
                    "error": f"Row processing error: {str(e)}"
                })

        # Commit all successful records
        if created_records:
            db.commit()
        else:
            db.rollback()

        return {
            "message": f"Bulk upload completed",
            "total_rows": row_number - 1,
            "successful_records": len(created_records),
            "failed_records": len(errors),
            "created_records": created_records,
            "errors": errors
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error processing CSV file: {str(e)}")


def convert_csv_value(value: str, data_type: str):
    """Convert CSV string value to appropriate Python type based on data type."""
    if not value or str(value).strip() == "":
        return None

    value = str(value).strip()

    try:
        if data_type == "Number":
            return int(value)
        elif data_type == "Decimal":
            return float(value)
        elif data_type == "Boolean":
            return value.lower() in ("true", "1", "yes", "on", "y")
        elif data_type in ("Date", "Datetime"):
            # For now, just return the string - could add date parsing later
            return value
        else:
            return value
    except (ValueError, TypeError) as e:
        raise ValueError(f"Cannot convert '{value}' to {data_type}")


# Advanced record filtering endpoints
@router.post("/{data_input_id}/records/filter", response_model=RecordFilterResponse)
def filter_records(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    filter_request: RecordFilterRequest,
    current_user: User = Depends(deps.get_current_active_user),
    brand_service: BrandAccessService = Depends(deps.get_brand_access_service),
) -> Any:
    """
    Filter records with advanced column-based filtering, searching, and sorting.

    This endpoint provides comprehensive filtering capabilities:
    - Column-based filters with various operators (equals, contains, greater than, etc.)
    - Global search across text fields
    - Multi-column sorting
    - Pagination with metadata
    - Filter validation against attribute definitions
    """
    # Check if data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check brand access
    check_data_input_brand_access(
        data_input_id, "view", current_user, brand_service, db
    )

    # Ensure data_input_id matches
    filter_request.data_input_id = data_input_id

    # Use record filter service
    filter_service = RecordFilterService(db)

    try:
        result = filter_service.filter_records(filter_request)
        return result
    except ValueError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Filtering failed: {str(e)}")


@router.get("/{data_input_id}/records/search")
def search_records(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    q: Optional[str] = Query(None, description="Search query"),
    columns: Optional[str] = Query(None, description="Comma-separated list of columns to search"),
    sort_by: Optional[str] = Query(None, description="Column to sort by"),
    sort_order: str = Query("desc", description="Sort order: asc or desc"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    current_user: User = Depends(deps.get_current_active_user),
    brand_service: BrandAccessService = Depends(deps.get_brand_access_service),
) -> Any:
    """
    Search records with simple query parameters.

    This is a simplified version of the filter endpoint for basic searching and sorting.
    For advanced filtering, use the POST /records/filter endpoint.
    """
    # Check if data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check brand access
    check_data_input_brand_access(
        data_input_id, "view", current_user, brand_service, db
    )

    # Parse search columns
    search_columns = None
    if columns:
        search_columns = [col.strip() for col in columns.split(",")]

    # Use enhanced CRUD method
    result = crud_record.get_by_data_input_with_filters(
        db,
        data_input_id=data_input_id,
        search_query=q,
        sort_by=sort_by,
        sort_order=sort_order,
        skip=skip,
        limit=limit
    )

    return {
        "records": result["records"],
        "total_count": result["total_count"],
        "filtered_count": result["filtered_count"],
        "page": result["page"],
        "page_size": result["page_size"],
        "total_pages": result["total_pages"],
        "has_next": result["has_next"],
        "has_previous": result["has_previous"],
        "search_query": q,
        "sort_by": sort_by,
        "sort_order": sort_order
    }


@router.post("/{data_input_id}/records/validate-filters", response_model=FilterValidationResponse)
def validate_record_filters(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    validation_request: FilterValidationRequest,
    current_user: User = Depends(deps.get_current_active_user),
    brand_service: BrandAccessService = Depends(deps.get_brand_access_service),
) -> Any:
    """
    Validate filters against data input attribute definitions.

    This endpoint helps validate filter configurations before applying them,
    providing information about available columns, valid operators, and any errors.
    """
    # Check if data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check brand access
    check_data_input_brand_access(
        data_input_id, "view", current_user, brand_service, db
    )

    # Ensure data_input_id matches
    validation_request.data_input_id = data_input_id

    # Use record filter service
    filter_service = RecordFilterService(db)

    try:
        result = filter_service.validate_filters(validation_request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Filter validation failed: {str(e)}")


@router.get("/{data_input_id}/records/statistics", response_model=RecordStatisticsResponse)
def get_record_statistics(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    brand_service: BrandAccessService = Depends(deps.get_brand_access_service),
) -> Any:
    """
    Get comprehensive statistics for all columns in the data input.

    This endpoint provides:
    - Record counts (total, active, deleted)
    - Column statistics (null counts, unique values, min/max/avg)
    - Most common values for each column
    - Data type information
    """
    # Check if data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check brand access
    check_data_input_brand_access(
        data_input_id, "view", current_user, brand_service, db
    )

    # Use record filter service
    filter_service = RecordFilterService(db)

    try:
        result = filter_service.get_column_statistics(data_input_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Statistics generation failed: {str(e)}")


@router.post("/{data_input_id}/records/export", response_model=ExportResponse)
def export_records(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    export_request: ExportRequest,
    current_user: User = Depends(deps.get_current_active_user),
    brand_service: BrandAccessService = Depends(deps.get_brand_access_service),
) -> Any:
    """
    Export filtered records to various formats.

    This endpoint allows users to export filtered data in multiple formats:
    - CSV: Comma-separated values for spreadsheet applications
    - XLSX: Excel format (requires pandas and openpyxl)
    - JSON: JavaScript Object Notation for programmatic use

    Features:
    - Apply any combination of filters before export
    - Select specific columns to include
    - Choose export format and options
    - Secure file storage with access controls
    - Download tracking and audit logging
    """
    # Check if data input exists
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Check brand access
    check_data_input_brand_access(
        data_input_id, "view", current_user, brand_service, db
    )

    # Ensure data_input_id matches
    export_request.filter_request.data_input_id = data_input_id

    # Import and use export service
    from app.services.data_export import DataExportService
    export_service = DataExportService(db)

    try:
        result = export_service.create_export(
            export_request=export_request,
            requested_by=current_user.username,
            brand_id=data_input.brand_id
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Export creation failed: {str(e)}")


@router.put("/{data_input_id}", response_model=DataInput)
def update_data_input(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    data_input_in: DataInputUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a data input.
    """
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    data_input = crud_data_input.update(
        db, db_obj=data_input, obj_in=data_input_in, updated_by=current_user.username
    )
    return data_input


@router.delete("/{data_input_id}")
def delete_data_input(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Soft delete a data input.
    """
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    # Soft delete the data input
    data_input = crud_data_input.soft_delete(db, id=data_input_id, deleted_by=current_user.username)
    return {"message": "Data input deleted successfully"}


@router.delete("/{data_input_id}", response_model=DataInput)
def delete_data_input(
    *,
    db: Session = Depends(get_db),
    data_input_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Soft delete a data input.
    """
    data_input = crud_data_input.get(db, id=data_input_id)
    if not data_input:
        raise HTTPException(status_code=404, detail="Data input not found")

    data_input = crud_data_input.soft_delete(db, id=data_input_id, deleted_by=current_user.username)
    return data_input
