from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.api import deps
from app.core import security
from app.core.config import settings
from app.core.database import get_db
from app.crud import user as crud_user
from app.schemas.user import Token, UserLogin, User as UserSchema


def serialize_user(user) -> dict:
    """Convert User model to dict with role information"""
    role_name = None
    if user.user_roles:
        role_name = user.user_roles[0].role.name if user.user_roles[0].role else None

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "active": user.active,
        "is_superuser": user.is_superuser,
        "role": role_name,
        "created_on": user.created_on.isoformat() if user.created_on else None,
        "changed_on": user.changed_on.isoformat() if user.changed_on else None,
        "last_login": user.last_login.isoformat() if user.last_login else None,
        "login_count": user.login_count,
        "fail_login_count": user.fail_login_count,
    }

router = APIRouter()


@router.post("/login")
def login_for_access_token(
    db: Session = Depends(get_db), 
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = crud_user.authenticate(
        db, username=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    elif not crud_user.is_active(user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )

    # Reload user with roles after authentication updates
    user = crud_user.get_with_roles(db, id=user.id)

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            user.username, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
        "user": serialize_user(user),
    }


@router.post("/login/json")
def login_json(
    user_login: UserLogin,
    db: Session = Depends(get_db)
) -> Any:
    """
    JSON login endpoint
    """
    user = crud_user.authenticate(
        db, username=user_login.username, password=user_login.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    elif not crud_user.is_active(user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="Inactive user"
        )
    # Reload user with roles after authentication updates
    user = crud_user.get_with_roles(db, id=user.id)

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            user.username, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
        "user": serialize_user(user),
    }


@router.get("/me")
def get_current_user_info(
    current_user: Any = Depends(deps.get_current_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get current user information
    """
    # Reload user with roles
    user = crud_user.get_with_roles(db, id=current_user.id)
    return serialize_user(user)


@router.post("/test-token")
def test_token(current_user: Any = Depends(deps.get_current_user)) -> Any:
    """
    Test access token
    """
    return current_user
