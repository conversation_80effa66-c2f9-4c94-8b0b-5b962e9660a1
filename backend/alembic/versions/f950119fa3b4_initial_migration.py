"""Initial migration

Revision ID: f950119fa3b4
Revises: 
Create Date: 2025-06-30 22:59:00.868049

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f950119fa3b4'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_brand_access_audit_action', table_name='brand_access_audit')
    op.drop_index('idx_brand_access_audit_brand', table_name='brand_access_audit')
    op.drop_index('idx_brand_access_audit_performed_at', table_name='brand_access_audit')
    op.drop_index('idx_brand_access_audit_user', table_name='brand_access_audit')
    op.drop_table('brand_access_audit')
    op.create_index(op.f('ix_audit_log_id'), 'audit_log', ['id'], unique=False)
    op.create_index(op.f('ix_brands_id'), 'brands', ['id'], unique=False)
    op.alter_column('data_exports', 'export_id',
               existing_type=sa.VARCHAR(length=64),
               comment=None,
               existing_comment='Public UUID for referencing exports',
               existing_nullable=False)
    op.alter_column('data_exports', 'export_type',
               existing_type=postgresql.ENUM('records', 'statistics', 'template', name='export_type'),
               type_=sa.String(length=50),
               existing_nullable=False,
               existing_server_default=sa.text("'records'::export_type"))
    op.alter_column('data_exports', 'export_format',
               existing_type=postgresql.ENUM('csv', 'xlsx', 'json', 'pdf', name='export_format'),
               type_=sa.String(length=20),
               existing_nullable=False)
    op.alter_column('data_exports', 'selected_columns',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='Array of column names to include in export',
               existing_nullable=True)
    op.alter_column('data_exports', 'filter_config',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='JSON configuration of applied filters',
               existing_nullable=True)
    op.alter_column('data_exports', 'status',
               existing_type=postgresql.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', name='export_status'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::export_status"))
    op.alter_column('data_exports', 'file_size',
               existing_type=sa.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
    op.alter_column('data_exports', 'export_options',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='Format-specific export options',
               existing_nullable=True)
    op.alter_column('data_exports', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('data_exports', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_constraint('data_exports_export_id_key', 'data_exports', type_='unique')
    op.drop_index('idx_data_exports_brand_id', table_name='data_exports')
    op.drop_index('idx_data_exports_created_at', table_name='data_exports')
    op.drop_index('idx_data_exports_data_input_id', table_name='data_exports')
    op.drop_index('idx_data_exports_expires_at', table_name='data_exports')
    op.drop_index('idx_data_exports_export_id', table_name='data_exports')
    op.drop_index('idx_data_exports_requested_by', table_name='data_exports')
    op.drop_index('idx_data_exports_status', table_name='data_exports')
    op.create_index(op.f('ix_data_exports_export_id'), 'data_exports', ['export_id'], unique=True)
    op.create_index(op.f('ix_data_exports_id'), 'data_exports', ['id'], unique=False)
    op.drop_constraint('data_exports_file_id_fkey', 'data_exports', type_='foreignkey')
    op.drop_constraint('data_exports_brand_id_fkey', 'data_exports', type_='foreignkey')
    op.drop_constraint('data_exports_data_input_id_fkey', 'data_exports', type_='foreignkey')
    op.create_foreign_key(None, 'data_exports', 'brands', ['brand_id'], ['id'])
    op.create_foreign_key(None, 'data_exports', 'files', ['file_id'], ['id'])
    op.create_foreign_key(None, 'data_exports', 'data_inputs', ['data_input_id'], ['id'])
    op.drop_table_comment(
        'data_exports',
        existing_comment='Tracks data export jobs with filtering and format options',
        schema=None
    )
    op.drop_constraint('data_input_attribute_pending_requests_data_input_id_name_key', 'data_input_attribute_pending_requests', type_='unique')
    op.create_index(op.f('ix_data_input_attribute_pending_requests_id'), 'data_input_attribute_pending_requests', ['id'], unique=False)
    op.drop_constraint('data_input_attribute_pending_requests_data_input_id_fkey', 'data_input_attribute_pending_requests', type_='foreignkey')
    op.drop_constraint('data_input_attribute_pending__relationship_target_data_inp_fkey', 'data_input_attribute_pending_requests', type_='foreignkey')
    op.drop_constraint('data_input_attribute_pending__data_input_pending_request_i_fkey', 'data_input_attribute_pending_requests', type_='foreignkey')
    op.create_foreign_key(None, 'data_input_attribute_pending_requests', 'data_inputs', ['data_input_id'], ['id'])
    op.create_foreign_key(None, 'data_input_attribute_pending_requests', 'data_inputs', ['relationship_target_data_input_id'], ['id'])
    op.create_foreign_key(None, 'data_input_attribute_pending_requests', 'data_input_pending_requests', ['data_input_pending_request_id'], ['id'])
    op.alter_column('data_input_attributes', 'data_type',
               existing_type=postgresql.ENUM('Text', 'Number', 'Decimal', 'Boolean', 'Date', 'Datetime', 'File', 'Image', 'Video', 'Audio', 'URL', 'Email', name='data_type_enum'),
               type_=sa.String(length=50),
               existing_nullable=False,
               existing_server_default=sa.text("'Text'::data_type_enum"))
    op.drop_constraint('data_input_attributes_data_input_id_name_key', 'data_input_attributes', type_='unique')
    op.drop_index('idx_data_input_attributes_data_type', table_name='data_input_attributes')
    op.drop_index('idx_data_input_attributes_display_order', table_name='data_input_attributes')
    op.drop_index('idx_data_input_attributes_required', table_name='data_input_attributes')
    op.drop_index('idx_data_input_attributes_unique', table_name='data_input_attributes')
    op.create_index(op.f('ix_data_input_attributes_id'), 'data_input_attributes', ['id'], unique=False)
    op.drop_constraint('data_input_attributes_data_input_id_fkey', 'data_input_attributes', type_='foreignkey')
    op.drop_constraint('data_input_attributes_relationship_target_data_input_id_fkey', 'data_input_attributes', type_='foreignkey')
    op.create_foreign_key(None, 'data_input_attributes', 'data_inputs', ['relationship_target_data_input_id'], ['id'])
    op.create_foreign_key(None, 'data_input_attributes', 'data_inputs', ['data_input_id'], ['id'])
    op.add_column('data_input_audit_logs', sa.Column('audit_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('data_input_audit_logs', 'severity',
               existing_type=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.alter_column('data_input_audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('data_input_audit_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_data_input_audit_logs_brand_id', table_name='data_input_audit_logs')
    op.drop_index('idx_data_input_audit_logs_resource_type', table_name='data_input_audit_logs')
    op.drop_index('idx_data_input_audit_logs_severity', table_name='data_input_audit_logs')
    op.drop_index('idx_data_input_audit_logs_success', table_name='data_input_audit_logs')
    op.create_index(op.f('ix_data_input_audit_logs_id'), 'data_input_audit_logs', ['id'], unique=False)
    op.drop_constraint('data_input_audit_logs_data_input_id_fkey', 'data_input_audit_logs', type_='foreignkey')
    op.drop_constraint('data_input_audit_logs_brand_id_fkey', 'data_input_audit_logs', type_='foreignkey')
    op.drop_constraint('data_input_audit_logs_user_id_fkey', 'data_input_audit_logs', type_='foreignkey')
    op.create_foreign_key(None, 'data_input_audit_logs', 'brands', ['brand_id'], ['id'])
    op.create_foreign_key(None, 'data_input_audit_logs', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'data_input_audit_logs', 'data_inputs', ['data_input_id'], ['id'])
    op.drop_table_comment(
        'data_input_audit_logs',
        existing_comment='Audit log for data input, attribute, and record operations',
        schema=None
    )
    op.drop_column('data_input_audit_logs', 'metadata')
    op.create_index(op.f('ix_data_input_pending_requests_id'), 'data_input_pending_requests', ['id'], unique=False)
    op.drop_constraint('data_input_pending_requests_brand_id_fkey', 'data_input_pending_requests', type_='foreignkey')
    op.create_foreign_key(None, 'data_input_pending_requests', 'brands', ['brand_id'], ['id'])
    op.alter_column('data_inputs', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='Timestamp when the record was soft deleted',
               existing_nullable=True)
    op.alter_column('data_inputs', 'deleted_by',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='Username of the user who soft deleted the record',
               existing_nullable=True)
    op.alter_column('data_inputs', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Flag indicating if the record is soft deleted',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_index('idx_data_inputs_is_deleted', table_name='data_inputs')
    op.create_index(op.f('ix_data_inputs_id'), 'data_inputs', ['id'], unique=False)
    op.drop_constraint('data_inputs_brand_id_fkey', 'data_inputs', type_='foreignkey')
    op.create_foreign_key(None, 'data_inputs', 'brands', ['brand_id'], ['id'])
    op.add_column('export_access_logs', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('export_access_logs', sa.Column('created_by', sa.String(length=255), nullable=True))
    op.add_column('export_access_logs', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('export_access_logs', sa.Column('updated_by', sa.String(length=255), nullable=True))
    op.alter_column('export_access_logs', 'ip_address',
               existing_type=postgresql.INET(),
               type_=sa.String(length=45),
               existing_nullable=True)
    op.alter_column('export_access_logs', 'bytes_transferred',
               existing_type=sa.BIGINT(),
               type_=sa.Integer(),
               existing_nullable=True)
    op.alter_column('export_access_logs', 'accessed_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_export_access_logs_access_type', table_name='export_access_logs')
    op.drop_index('idx_export_access_logs_accessed_at', table_name='export_access_logs')
    op.drop_index('idx_export_access_logs_export_id', table_name='export_access_logs')
    op.create_index(op.f('ix_export_access_logs_id'), 'export_access_logs', ['id'], unique=False)
    op.drop_constraint('export_access_logs_export_id_fkey', 'export_access_logs', type_='foreignkey')
    op.create_foreign_key(None, 'export_access_logs', 'data_exports', ['export_id'], ['id'])
    op.drop_table_comment(
        'export_access_logs',
        existing_comment='Audit log for export file access and downloads',
        schema=None
    )
    op.alter_column('export_schedules', 'schedule_type',
               existing_type=postgresql.ENUM('daily', 'weekly', 'monthly', 'custom', name='schedule_type'),
               type_=sa.String(length=20),
               existing_nullable=False)
    op.alter_column('export_schedules', 'export_format',
               existing_type=postgresql.ENUM('csv', 'xlsx', 'json', 'pdf', name='export_format'),
               type_=sa.String(length=20),
               existing_nullable=False)
    op.alter_column('export_schedules', 'delivery_method',
               existing_type=postgresql.ENUM('download', 'email', 'ftp', 's3', name='delivery_method'),
               type_=sa.String(length=20),
               existing_nullable=True,
               existing_server_default=sa.text("'download'::delivery_method"))
    op.alter_column('export_schedules', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_schedules', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_export_schedules_data_input_id', table_name='export_schedules')
    op.drop_index('idx_export_schedules_is_active', table_name='export_schedules')
    op.drop_index('idx_export_schedules_next_run_at', table_name='export_schedules')
    op.create_index(op.f('ix_export_schedules_id'), 'export_schedules', ['id'], unique=False)
    op.drop_constraint('export_schedules_data_input_id_fkey', 'export_schedules', type_='foreignkey')
    op.create_foreign_key(None, 'export_schedules', 'data_inputs', ['data_input_id'], ['id'])
    op.drop_table_comment(
        'export_schedules',
        existing_comment='Scheduled/recurring export configurations',
        schema=None
    )
    op.add_column('export_shares', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('export_shares', sa.Column('updated_by', sa.String(length=255), nullable=True))
    op.alter_column('export_shares', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_constraint('export_shares_share_token_key', 'export_shares', type_='unique')
    op.drop_index('idx_export_shares_expires_at', table_name='export_shares')
    op.drop_index('idx_export_shares_export_id', table_name='export_shares')
    op.drop_index('idx_export_shares_share_token', table_name='export_shares')
    op.create_index(op.f('ix_export_shares_id'), 'export_shares', ['id'], unique=False)
    op.create_index(op.f('ix_export_shares_share_token'), 'export_shares', ['share_token'], unique=True)
    op.drop_constraint('export_shares_export_id_fkey', 'export_shares', type_='foreignkey')
    op.create_foreign_key(None, 'export_shares', 'data_exports', ['export_id'], ['id'])
    op.drop_table_comment(
        'export_shares',
        existing_comment='Secure sharing of export files with external users',
        schema=None
    )
    op.alter_column('export_templates', 'export_format',
               existing_type=postgresql.ENUM('csv', 'xlsx', 'json', 'pdf', name='export_format'),
               type_=sa.String(length=20),
               existing_nullable=False)
    op.alter_column('export_templates', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_templates', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_export_templates_data_input_id', table_name='export_templates')
    op.drop_index('idx_export_templates_is_active', table_name='export_templates')
    op.drop_index('idx_export_templates_is_public', table_name='export_templates')
    op.create_index(op.f('ix_export_templates_id'), 'export_templates', ['id'], unique=False)
    op.drop_constraint('export_templates_data_input_id_fkey', 'export_templates', type_='foreignkey')
    op.create_foreign_key(None, 'export_templates', 'data_inputs', ['data_input_id'], ['id'])
    op.drop_table_comment(
        'export_templates',
        existing_comment='Reusable export configurations for common use cases',
        schema=None
    )
    op.add_column('file_access_logs', sa.Column('access_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('file_access_logs', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.alter_column('file_access_logs', 'ip_address',
               existing_type=postgresql.INET(),
               type_=sa.String(length=45),
               existing_nullable=True)
    op.alter_column('file_access_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_file_access_logs_access_type', table_name='file_access_logs')
    op.drop_index('idx_file_access_logs_accessed_by', table_name='file_access_logs')
    op.drop_index('idx_file_access_logs_created_at', table_name='file_access_logs')
    op.drop_index('idx_file_access_logs_file_id', table_name='file_access_logs')
    op.create_index(op.f('ix_file_access_logs_id'), 'file_access_logs', ['id'], unique=False)
    op.drop_constraint('file_access_logs_file_id_fkey', 'file_access_logs', type_='foreignkey')
    op.create_foreign_key(None, 'file_access_logs', 'files', ['file_id'], ['id'])
    op.drop_column('file_access_logs', 'metadata')
    op.alter_column('file_processing_jobs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_processing_jobs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_file_processing_jobs_file_id', table_name='file_processing_jobs')
    op.drop_index('idx_file_processing_jobs_priority', table_name='file_processing_jobs')
    op.drop_index('idx_file_processing_jobs_status', table_name='file_processing_jobs')
    op.create_index(op.f('ix_file_processing_jobs_id'), 'file_processing_jobs', ['id'], unique=False)
    op.drop_constraint('file_processing_jobs_file_id_fkey', 'file_processing_jobs', type_='foreignkey')
    op.create_foreign_key(None, 'file_processing_jobs', 'files', ['file_id'], ['id'])
    op.alter_column('file_records', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_records', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_constraint('file_records_file_id_record_id_attribute_name_key', 'file_records', type_='unique')
    op.drop_index('idx_file_records_attribute_name', table_name='file_records')
    op.drop_index('idx_file_records_file_id', table_name='file_records')
    op.drop_index('idx_file_records_record_id', table_name='file_records')
    op.create_index(op.f('ix_file_records_id'), 'file_records', ['id'], unique=False)
    op.drop_constraint('file_records_record_id_fkey', 'file_records', type_='foreignkey')
    op.drop_constraint('file_records_file_id_fkey', 'file_records', type_='foreignkey')
    op.create_foreign_key(None, 'file_records', 'records', ['record_id'], ['id'])
    op.create_foreign_key(None, 'file_records', 'files', ['file_id'], ['id'])
    op.add_column('file_shares', sa.Column('updated_by', sa.String(length=255), nullable=True))
    op.alter_column('file_shares', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_shares', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_file_shares_expires_at', table_name='file_shares')
    op.drop_index('idx_file_shares_file_id', table_name='file_shares')
    op.drop_index('idx_file_shares_is_active', table_name='file_shares')
    op.drop_index('idx_file_shares_share_token', table_name='file_shares')
    op.create_index(op.f('ix_file_shares_id'), 'file_shares', ['id'], unique=False)
    op.drop_constraint('file_shares_file_id_fkey', 'file_shares', type_='foreignkey')
    op.create_foreign_key(None, 'file_shares', 'files', ['file_id'], ['id'])
    op.alter_column('file_versions', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_versions', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_constraint('file_versions_file_id_version_number_key', 'file_versions', type_='unique')
    op.drop_index('idx_file_versions_file_id', table_name='file_versions')
    op.drop_index('idx_file_versions_version_number', table_name='file_versions')
    op.create_index(op.f('ix_file_versions_id'), 'file_versions', ['id'], unique=False)
    op.drop_constraint('file_versions_file_id_fkey', 'file_versions', type_='foreignkey')
    op.create_foreign_key(None, 'file_versions', 'files', ['file_id'], ['id'])
    op.alter_column('files', 'file_type',
               existing_type=postgresql.ENUM('image', 'document', 'audio', 'video', 'archive', 'other', name='file_type'),
               type_=sa.String(length=20),
               existing_nullable=False)
    op.alter_column('files', 'status',
               existing_type=postgresql.ENUM('uploading', 'processing', 'ready', 'error', 'quarantined', 'deleted', name='file_status'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'uploading'::file_status"))
    op.alter_column('files', 'storage_backend',
               existing_type=postgresql.ENUM('local', 's3', 'gcs', name='storage_backend'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'local'::storage_backend"))
    op.alter_column('files', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('files', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('files', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('files', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_index('idx_files_brand_id', table_name='files')
    op.drop_index('idx_files_checksum_md5', table_name='files')
    op.drop_index('idx_files_created_at', table_name='files')
    op.drop_index('idx_files_file_type', table_name='files')
    op.drop_index('idx_files_filename', table_name='files')
    op.drop_index('idx_files_is_deleted', table_name='files')
    op.drop_index('idx_files_is_public', table_name='files')
    op.drop_index('idx_files_status', table_name='files')
    op.drop_index('idx_files_uploaded_by', table_name='files')
    op.create_index(op.f('ix_files_id'), 'files', ['id'], unique=False)
    op.alter_column('records', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment=None,
               existing_comment='Timestamp when the record was soft deleted',
               existing_nullable=True)
    op.alter_column('records', 'deleted_by',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='Username of the user who soft deleted the record',
               existing_nullable=True)
    op.alter_column('records', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='Flag indicating if the record is soft deleted',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_index('idx_records_is_deleted', table_name='records')
    op.create_index(op.f('ix_records_id'), 'records', ['id'], unique=False)
    op.drop_constraint('records_data_input_id_fkey', 'records', type_='foreignkey')
    op.create_foreign_key(None, 'records', 'data_inputs', ['data_input_id'], ['id'])
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.add_column('security_audit_logs', sa.Column('audit_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('security_audit_logs', 'severity',
               existing_type=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'high'::audit_severity"))
    op.alter_column('security_audit_logs', 'risk_score',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Risk score from 1-100 based on threat assessment',
               existing_nullable=True)
    op.alter_column('security_audit_logs', 'threat_indicators',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='Array of detected threat indicators',
               existing_nullable=True)
    op.alter_column('security_audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('security_audit_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_security_audit_logs_event_category', table_name='security_audit_logs')
    op.drop_index('idx_security_audit_logs_event_type', table_name='security_audit_logs')
    op.drop_index('idx_security_audit_logs_username_attempted', table_name='security_audit_logs')
    op.create_index(op.f('ix_security_audit_logs_id'), 'security_audit_logs', ['id'], unique=False)
    op.drop_constraint('security_audit_logs_user_id_fkey', 'security_audit_logs', type_='foreignkey')
    op.create_foreign_key(None, 'security_audit_logs', 'users', ['user_id'], ['id'])
    op.drop_table_comment(
        'security_audit_logs',
        existing_comment='Audit log for security events, authentication, and authorization',
        schema=None
    )
    op.drop_column('security_audit_logs', 'metadata')
    op.add_column('system_audit_logs', sa.Column('audit_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('system_audit_logs', 'severity',
               existing_type=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.alter_column('system_audit_logs', 'execution_time_ms',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='Operation execution time in milliseconds',
               existing_nullable=True)
    op.alter_column('system_audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('system_audit_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_system_audit_logs_operation_type', table_name='system_audit_logs')
    op.drop_index('idx_system_audit_logs_severity', table_name='system_audit_logs')
    op.drop_index('idx_system_audit_logs_success', table_name='system_audit_logs')
    op.create_index(op.f('ix_system_audit_logs_id'), 'system_audit_logs', ['id'], unique=False)
    op.drop_constraint('system_audit_logs_user_id_fkey', 'system_audit_logs', type_='foreignkey')
    op.create_foreign_key(None, 'system_audit_logs', 'users', ['user_id'], ['id'])
    op.drop_table_comment(
        'system_audit_logs',
        existing_comment='Audit log for system-level operations and configuration changes',
        schema=None
    )
    op.drop_column('system_audit_logs', 'metadata')
    op.add_column('user_audit_logs', sa.Column('audit_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('user_audit_logs', 'severity',
               existing_type=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.alter_column('user_audit_logs', 'changed_fields',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='Array of field names that were changed in the operation',
               existing_nullable=True)
    op.alter_column('user_audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_audit_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_user_audit_logs_resource_id', table_name='user_audit_logs')
    op.drop_index('idx_user_audit_logs_resource_type', table_name='user_audit_logs')
    op.drop_index('idx_user_audit_logs_target_user_id', table_name='user_audit_logs')
    op.create_index(op.f('ix_user_audit_logs_id'), 'user_audit_logs', ['id'], unique=False)
    op.drop_constraint('user_audit_logs_user_id_fkey', 'user_audit_logs', type_='foreignkey')
    op.drop_constraint('user_audit_logs_target_user_id_fkey', 'user_audit_logs', type_='foreignkey')
    op.create_foreign_key(None, 'user_audit_logs', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'user_audit_logs', 'users', ['target_user_id'], ['id'])
    op.drop_table_comment(
        'user_audit_logs',
        existing_comment='Audit log for user-related operations and changes',
        schema=None
    )
    op.drop_column('user_audit_logs', 'metadata')
    op.alter_column('user_brands', 'brand_role',
               existing_type=postgresql.ENUM('viewer', 'editor', 'admin', 'owner', name='brand_role'),
               type_=sa.Enum('VIEWER', 'EDITOR', 'ADMIN', 'OWNER', name='brandrole'),
               existing_nullable=False,
               existing_server_default=sa.text("'viewer'::brand_role"))
    op.drop_index('idx_user_brands_active', table_name='user_brands')
    op.drop_index('idx_user_brands_permissions', table_name='user_brands')
    op.drop_index('idx_user_brands_role', table_name='user_brands')
    op.drop_constraint('user_brands_user_id_brand_id_key', 'user_brands', type_='unique')
    op.create_index(op.f('ix_user_brands_id'), 'user_brands', ['id'], unique=False)
    op.drop_constraint('user_brands_brand_id_fkey', 'user_brands', type_='foreignkey')
    op.drop_constraint('user_brands_user_id_fkey', 'user_brands', type_='foreignkey')
    op.create_foreign_key(None, 'user_brands', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'user_brands', 'brands', ['brand_id'], ['id'])
    op.drop_constraint('user_roles_user_id_role_id_key', 'user_roles', type_='unique')
    op.create_index(op.f('ix_user_roles_id'), 'user_roles', ['id'], unique=False)
    op.drop_constraint('user_roles_role_id_fkey', 'user_roles', type_='foreignkey')
    op.drop_constraint('user_roles_user_id_fkey', 'user_roles', type_='foreignkey')
    op.create_foreign_key(None, 'user_roles', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'user_roles', 'roles', ['role_id'], ['id'])
    op.drop_constraint('users_email_key', 'users', type_='unique')
    op.drop_constraint('users_username_key', 'users', type_='unique')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.drop_column('users', 'updated_at')
    op.drop_column('users', 'updated_by')
    op.drop_column('users', 'created_by')
    op.drop_column('users', 'created_at')
    op.add_column('workflow_audit_logs', sa.Column('audit_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('workflow_audit_logs', 'severity',
               existing_type=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               type_=sa.String(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.alter_column('workflow_audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('workflow_audit_logs', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_workflow_audit_logs_new_status', table_name='workflow_audit_logs')
    op.drop_index('idx_workflow_audit_logs_old_status', table_name='workflow_audit_logs')
    op.drop_index('idx_workflow_audit_logs_severity', table_name='workflow_audit_logs')
    op.drop_index('idx_workflow_audit_logs_success', table_name='workflow_audit_logs')
    op.create_index(op.f('ix_workflow_audit_logs_id'), 'workflow_audit_logs', ['id'], unique=False)
    op.drop_constraint('workflow_audit_logs_request_id_fkey', 'workflow_audit_logs', type_='foreignkey')
    op.drop_constraint('workflow_audit_logs_brand_id_fkey', 'workflow_audit_logs', type_='foreignkey')
    op.drop_constraint('workflow_audit_logs_user_id_fkey', 'workflow_audit_logs', type_='foreignkey')
    op.create_foreign_key(None, 'workflow_audit_logs', 'brands', ['brand_id'], ['id'])
    op.create_foreign_key(None, 'workflow_audit_logs', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'workflow_audit_logs', 'workflow_requests', ['request_id'], ['id'])
    op.drop_table_comment(
        'workflow_audit_logs',
        existing_comment='Audit log for workflow request operations and status changes',
        schema=None
    )
    op.drop_column('workflow_audit_logs', 'metadata')
    op.alter_column('workflow_requests', 'request_type',
               existing_type=postgresql.ENUM('create_data_input', 'update_data_input', 'delete_data_input', 'create_attribute', 'update_attribute', 'delete_attribute', 'create_brand', 'update_brand', 'delete_brand', name='request_type'),
               type_=sa.Enum('CREATE_DATA_INPUT', 'UPDATE_DATA_INPUT', 'DELETE_DATA_INPUT', 'CREATE_ATTRIBUTE', 'UPDATE_ATTRIBUTE', 'DELETE_ATTRIBUTE', 'CREATE_BRAND', 'UPDATE_BRAND', 'DELETE_BRAND', name='requesttype'),
               existing_nullable=False)
    op.alter_column('workflow_requests', 'status',
               existing_type=postgresql.ENUM('pending', 'under_review', 'approved', 'rejected', 'cancelled', 'implemented', name='request_status'),
               type_=sa.Enum('PENDING', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'CANCELLED', 'IMPLEMENTED', name='requeststatus'),
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::request_status"))
    op.alter_column('workflow_requests', 'priority',
               existing_type=postgresql.ENUM('low', 'medium', 'high', 'urgent', name='request_priority'),
               type_=sa.Enum('LOW', 'MEDIUM', 'HIGH', 'URGENT', name='requestpriority'),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::request_priority"))
    op.drop_index('idx_workflow_requests_approver', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_brand', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_created_at', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_data_input', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_priority', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_requester', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_reviewer', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_status', table_name='workflow_requests')
    op.drop_index('idx_workflow_requests_type', table_name='workflow_requests')
    op.create_index(op.f('ix_workflow_requests_id'), 'workflow_requests', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_workflow_requests_id'), table_name='workflow_requests')
    op.create_index('idx_workflow_requests_type', 'workflow_requests', ['request_type'], unique=False)
    op.create_index('idx_workflow_requests_status', 'workflow_requests', ['status'], unique=False)
    op.create_index('idx_workflow_requests_reviewer', 'workflow_requests', ['reviewer_id'], unique=False)
    op.create_index('idx_workflow_requests_requester', 'workflow_requests', ['requester_id'], unique=False)
    op.create_index('idx_workflow_requests_priority', 'workflow_requests', ['priority'], unique=False)
    op.create_index('idx_workflow_requests_data_input', 'workflow_requests', ['data_input_id'], unique=False)
    op.create_index('idx_workflow_requests_created_at', 'workflow_requests', ['created_at'], unique=False)
    op.create_index('idx_workflow_requests_brand', 'workflow_requests', ['brand_id'], unique=False)
    op.create_index('idx_workflow_requests_approver', 'workflow_requests', ['approver_id'], unique=False)
    op.alter_column('workflow_requests', 'priority',
               existing_type=sa.Enum('LOW', 'MEDIUM', 'HIGH', 'URGENT', name='requestpriority'),
               type_=postgresql.ENUM('low', 'medium', 'high', 'urgent', name='request_priority'),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::request_priority"))
    op.alter_column('workflow_requests', 'status',
               existing_type=sa.Enum('PENDING', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'CANCELLED', 'IMPLEMENTED', name='requeststatus'),
               type_=postgresql.ENUM('pending', 'under_review', 'approved', 'rejected', 'cancelled', 'implemented', name='request_status'),
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::request_status"))
    op.alter_column('workflow_requests', 'request_type',
               existing_type=sa.Enum('CREATE_DATA_INPUT', 'UPDATE_DATA_INPUT', 'DELETE_DATA_INPUT', 'CREATE_ATTRIBUTE', 'UPDATE_ATTRIBUTE', 'DELETE_ATTRIBUTE', 'CREATE_BRAND', 'UPDATE_BRAND', 'DELETE_BRAND', name='requesttype'),
               type_=postgresql.ENUM('create_data_input', 'update_data_input', 'delete_data_input', 'create_attribute', 'update_attribute', 'delete_attribute', 'create_brand', 'update_brand', 'delete_brand', name='request_type'),
               existing_nullable=False)
    op.add_column('workflow_audit_logs', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.create_table_comment(
        'workflow_audit_logs',
        'Audit log for workflow request operations and status changes',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'workflow_audit_logs', type_='foreignkey')
    op.drop_constraint(None, 'workflow_audit_logs', type_='foreignkey')
    op.drop_constraint(None, 'workflow_audit_logs', type_='foreignkey')
    op.create_foreign_key('workflow_audit_logs_user_id_fkey', 'workflow_audit_logs', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('workflow_audit_logs_brand_id_fkey', 'workflow_audit_logs', 'brands', ['brand_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('workflow_audit_logs_request_id_fkey', 'workflow_audit_logs', 'workflow_requests', ['request_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_workflow_audit_logs_id'), table_name='workflow_audit_logs')
    op.create_index('idx_workflow_audit_logs_success', 'workflow_audit_logs', ['success'], unique=False)
    op.create_index('idx_workflow_audit_logs_severity', 'workflow_audit_logs', ['severity'], unique=False)
    op.create_index('idx_workflow_audit_logs_old_status', 'workflow_audit_logs', ['old_status'], unique=False)
    op.create_index('idx_workflow_audit_logs_new_status', 'workflow_audit_logs', ['new_status'], unique=False)
    op.alter_column('workflow_audit_logs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('workflow_audit_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('workflow_audit_logs', 'severity',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.drop_column('workflow_audit_logs', 'audit_metadata')
    op.add_column('users', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('created_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('updated_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False))
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_unique_constraint('users_username_key', 'users', ['username'])
    op.create_unique_constraint('users_email_key', 'users', ['email'])
    op.drop_constraint(None, 'user_roles', type_='foreignkey')
    op.drop_constraint(None, 'user_roles', type_='foreignkey')
    op.create_foreign_key('user_roles_user_id_fkey', 'user_roles', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('user_roles_role_id_fkey', 'user_roles', 'roles', ['role_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_user_roles_id'), table_name='user_roles')
    op.create_unique_constraint('user_roles_user_id_role_id_key', 'user_roles', ['user_id', 'role_id'])
    op.drop_constraint(None, 'user_brands', type_='foreignkey')
    op.drop_constraint(None, 'user_brands', type_='foreignkey')
    op.create_foreign_key('user_brands_user_id_fkey', 'user_brands', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('user_brands_brand_id_fkey', 'user_brands', 'brands', ['brand_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_user_brands_id'), table_name='user_brands')
    op.create_unique_constraint('user_brands_user_id_brand_id_key', 'user_brands', ['user_id', 'brand_id'])
    op.create_index('idx_user_brands_role', 'user_brands', ['brand_role'], unique=False)
    op.create_index('idx_user_brands_permissions', 'user_brands', ['can_view', 'can_create', 'can_edit', 'can_delete'], unique=False)
    op.create_index('idx_user_brands_active', 'user_brands', ['is_active'], unique=False)
    op.alter_column('user_brands', 'brand_role',
               existing_type=sa.Enum('VIEWER', 'EDITOR', 'ADMIN', 'OWNER', name='brandrole'),
               type_=postgresql.ENUM('viewer', 'editor', 'admin', 'owner', name='brand_role'),
               existing_nullable=False,
               existing_server_default=sa.text("'viewer'::brand_role"))
    op.add_column('user_audit_logs', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.create_table_comment(
        'user_audit_logs',
        'Audit log for user-related operations and changes',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'user_audit_logs', type_='foreignkey')
    op.drop_constraint(None, 'user_audit_logs', type_='foreignkey')
    op.create_foreign_key('user_audit_logs_target_user_id_fkey', 'user_audit_logs', 'users', ['target_user_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('user_audit_logs_user_id_fkey', 'user_audit_logs', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_user_audit_logs_id'), table_name='user_audit_logs')
    op.create_index('idx_user_audit_logs_target_user_id', 'user_audit_logs', ['target_user_id'], unique=False)
    op.create_index('idx_user_audit_logs_resource_type', 'user_audit_logs', ['resource_type'], unique=False)
    op.create_index('idx_user_audit_logs_resource_id', 'user_audit_logs', ['resource_id'], unique=False)
    op.alter_column('user_audit_logs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_audit_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_audit_logs', 'changed_fields',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='Array of field names that were changed in the operation',
               existing_nullable=True)
    op.alter_column('user_audit_logs', 'severity',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.drop_column('user_audit_logs', 'audit_metadata')
    op.add_column('system_audit_logs', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.create_table_comment(
        'system_audit_logs',
        'Audit log for system-level operations and configuration changes',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'system_audit_logs', type_='foreignkey')
    op.create_foreign_key('system_audit_logs_user_id_fkey', 'system_audit_logs', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_system_audit_logs_id'), table_name='system_audit_logs')
    op.create_index('idx_system_audit_logs_success', 'system_audit_logs', ['success'], unique=False)
    op.create_index('idx_system_audit_logs_severity', 'system_audit_logs', ['severity'], unique=False)
    op.create_index('idx_system_audit_logs_operation_type', 'system_audit_logs', ['operation_type'], unique=False)
    op.alter_column('system_audit_logs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('system_audit_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('system_audit_logs', 'execution_time_ms',
               existing_type=sa.INTEGER(),
               comment='Operation execution time in milliseconds',
               existing_nullable=True)
    op.alter_column('system_audit_logs', 'severity',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.drop_column('system_audit_logs', 'audit_metadata')
    op.add_column('security_audit_logs', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.create_table_comment(
        'security_audit_logs',
        'Audit log for security events, authentication, and authorization',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'security_audit_logs', type_='foreignkey')
    op.create_foreign_key('security_audit_logs_user_id_fkey', 'security_audit_logs', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_security_audit_logs_id'), table_name='security_audit_logs')
    op.create_index('idx_security_audit_logs_username_attempted', 'security_audit_logs', ['username_attempted'], unique=False)
    op.create_index('idx_security_audit_logs_event_type', 'security_audit_logs', ['event_type'], unique=False)
    op.create_index('idx_security_audit_logs_event_category', 'security_audit_logs', ['event_category'], unique=False)
    op.alter_column('security_audit_logs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('security_audit_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('security_audit_logs', 'threat_indicators',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='Array of detected threat indicators',
               existing_nullable=True)
    op.alter_column('security_audit_logs', 'risk_score',
               existing_type=sa.INTEGER(),
               comment='Risk score from 1-100 based on threat assessment',
               existing_nullable=True)
    op.alter_column('security_audit_logs', 'severity',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               existing_nullable=False,
               existing_server_default=sa.text("'high'::audit_severity"))
    op.drop_column('security_audit_logs', 'audit_metadata')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_constraint(None, 'records', type_='foreignkey')
    op.create_foreign_key('records_data_input_id_fkey', 'records', 'data_inputs', ['data_input_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_records_id'), table_name='records')
    op.create_index('idx_records_is_deleted', 'records', ['is_deleted'], unique=False)
    op.alter_column('records', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               comment='Flag indicating if the record is soft deleted',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('records', 'deleted_by',
               existing_type=sa.VARCHAR(length=255),
               comment='Username of the user who soft deleted the record',
               existing_nullable=True)
    op.alter_column('records', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='Timestamp when the record was soft deleted',
               existing_nullable=True)
    op.drop_index(op.f('ix_files_id'), table_name='files')
    op.create_index('idx_files_uploaded_by', 'files', ['uploaded_by'], unique=False)
    op.create_index('idx_files_status', 'files', ['status'], unique=False)
    op.create_index('idx_files_is_public', 'files', ['is_public'], unique=False)
    op.create_index('idx_files_is_deleted', 'files', ['is_deleted'], unique=False)
    op.create_index('idx_files_filename', 'files', ['filename'], unique=False)
    op.create_index('idx_files_file_type', 'files', ['file_type'], unique=False)
    op.create_index('idx_files_created_at', 'files', ['created_at'], unique=False)
    op.create_index('idx_files_checksum_md5', 'files', ['checksum_md5'], unique=False)
    op.create_index('idx_files_brand_id', 'files', ['brand_id'], unique=False)
    op.alter_column('files', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('files', 'deleted_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
    op.alter_column('files', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('files', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('files', 'storage_backend',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('local', 's3', 'gcs', name='storage_backend'),
               existing_nullable=False,
               existing_server_default=sa.text("'local'::storage_backend"))
    op.alter_column('files', 'status',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('uploading', 'processing', 'ready', 'error', 'quarantined', 'deleted', name='file_status'),
               existing_nullable=False,
               existing_server_default=sa.text("'uploading'::file_status"))
    op.alter_column('files', 'file_type',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('image', 'document', 'audio', 'video', 'archive', 'other', name='file_type'),
               existing_nullable=False)
    op.drop_constraint(None, 'file_versions', type_='foreignkey')
    op.create_foreign_key('file_versions_file_id_fkey', 'file_versions', 'files', ['file_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_file_versions_id'), table_name='file_versions')
    op.create_index('idx_file_versions_version_number', 'file_versions', ['version_number'], unique=False)
    op.create_index('idx_file_versions_file_id', 'file_versions', ['file_id'], unique=False)
    op.create_unique_constraint('file_versions_file_id_version_number_key', 'file_versions', ['file_id', 'version_number'])
    op.alter_column('file_versions', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_versions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_constraint(None, 'file_shares', type_='foreignkey')
    op.create_foreign_key('file_shares_file_id_fkey', 'file_shares', 'files', ['file_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_file_shares_id'), table_name='file_shares')
    op.create_index('idx_file_shares_share_token', 'file_shares', ['share_token'], unique=False)
    op.create_index('idx_file_shares_is_active', 'file_shares', ['is_active'], unique=False)
    op.create_index('idx_file_shares_file_id', 'file_shares', ['file_id'], unique=False)
    op.create_index('idx_file_shares_expires_at', 'file_shares', ['expires_at'], unique=False)
    op.alter_column('file_shares', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_shares', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_column('file_shares', 'updated_by')
    op.drop_constraint(None, 'file_records', type_='foreignkey')
    op.drop_constraint(None, 'file_records', type_='foreignkey')
    op.create_foreign_key('file_records_file_id_fkey', 'file_records', 'files', ['file_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('file_records_record_id_fkey', 'file_records', 'records', ['record_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_file_records_id'), table_name='file_records')
    op.create_index('idx_file_records_record_id', 'file_records', ['record_id'], unique=False)
    op.create_index('idx_file_records_file_id', 'file_records', ['file_id'], unique=False)
    op.create_index('idx_file_records_attribute_name', 'file_records', ['attribute_name'], unique=False)
    op.create_unique_constraint('file_records_file_id_record_id_attribute_name_key', 'file_records', ['file_id', 'record_id', 'attribute_name'])
    op.alter_column('file_records', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_records', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_constraint(None, 'file_processing_jobs', type_='foreignkey')
    op.create_foreign_key('file_processing_jobs_file_id_fkey', 'file_processing_jobs', 'files', ['file_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_file_processing_jobs_id'), table_name='file_processing_jobs')
    op.create_index('idx_file_processing_jobs_status', 'file_processing_jobs', ['status'], unique=False)
    op.create_index('idx_file_processing_jobs_priority', 'file_processing_jobs', ['priority'], unique=False)
    op.create_index('idx_file_processing_jobs_file_id', 'file_processing_jobs', ['file_id'], unique=False)
    op.alter_column('file_processing_jobs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_processing_jobs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.add_column('file_access_logs', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'file_access_logs', type_='foreignkey')
    op.create_foreign_key('file_access_logs_file_id_fkey', 'file_access_logs', 'files', ['file_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_file_access_logs_id'), table_name='file_access_logs')
    op.create_index('idx_file_access_logs_file_id', 'file_access_logs', ['file_id'], unique=False)
    op.create_index('idx_file_access_logs_created_at', 'file_access_logs', ['created_at'], unique=False)
    op.create_index('idx_file_access_logs_accessed_by', 'file_access_logs', ['accessed_by'], unique=False)
    op.create_index('idx_file_access_logs_access_type', 'file_access_logs', ['access_type'], unique=False)
    op.alter_column('file_access_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('file_access_logs', 'ip_address',
               existing_type=sa.String(length=45),
               type_=postgresql.INET(),
               existing_nullable=True)
    op.drop_column('file_access_logs', 'updated_at')
    op.drop_column('file_access_logs', 'access_metadata')
    op.create_table_comment(
        'export_templates',
        'Reusable export configurations for common use cases',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'export_templates', type_='foreignkey')
    op.create_foreign_key('export_templates_data_input_id_fkey', 'export_templates', 'data_inputs', ['data_input_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_export_templates_id'), table_name='export_templates')
    op.create_index('idx_export_templates_is_public', 'export_templates', ['is_public'], unique=False)
    op.create_index('idx_export_templates_is_active', 'export_templates', ['is_active'], unique=False)
    op.create_index('idx_export_templates_data_input_id', 'export_templates', ['data_input_id'], unique=False)
    op.alter_column('export_templates', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_templates', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_templates', 'export_format',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('csv', 'xlsx', 'json', 'pdf', name='export_format'),
               existing_nullable=False)
    op.create_table_comment(
        'export_shares',
        'Secure sharing of export files with external users',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'export_shares', type_='foreignkey')
    op.create_foreign_key('export_shares_export_id_fkey', 'export_shares', 'data_exports', ['export_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_export_shares_share_token'), table_name='export_shares')
    op.drop_index(op.f('ix_export_shares_id'), table_name='export_shares')
    op.create_index('idx_export_shares_share_token', 'export_shares', ['share_token'], unique=False)
    op.create_index('idx_export_shares_export_id', 'export_shares', ['export_id'], unique=False)
    op.create_index('idx_export_shares_expires_at', 'export_shares', ['expires_at'], unique=False)
    op.create_unique_constraint('export_shares_share_token_key', 'export_shares', ['share_token'])
    op.alter_column('export_shares', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_column('export_shares', 'updated_by')
    op.drop_column('export_shares', 'updated_at')
    op.create_table_comment(
        'export_schedules',
        'Scheduled/recurring export configurations',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'export_schedules', type_='foreignkey')
    op.create_foreign_key('export_schedules_data_input_id_fkey', 'export_schedules', 'data_inputs', ['data_input_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_export_schedules_id'), table_name='export_schedules')
    op.create_index('idx_export_schedules_next_run_at', 'export_schedules', ['next_run_at'], unique=False)
    op.create_index('idx_export_schedules_is_active', 'export_schedules', ['is_active'], unique=False)
    op.create_index('idx_export_schedules_data_input_id', 'export_schedules', ['data_input_id'], unique=False)
    op.alter_column('export_schedules', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_schedules', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_schedules', 'delivery_method',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('download', 'email', 'ftp', 's3', name='delivery_method'),
               existing_nullable=True,
               existing_server_default=sa.text("'download'::delivery_method"))
    op.alter_column('export_schedules', 'export_format',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('csv', 'xlsx', 'json', 'pdf', name='export_format'),
               existing_nullable=False)
    op.alter_column('export_schedules', 'schedule_type',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('daily', 'weekly', 'monthly', 'custom', name='schedule_type'),
               existing_nullable=False)
    op.create_table_comment(
        'export_access_logs',
        'Audit log for export file access and downloads',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'export_access_logs', type_='foreignkey')
    op.create_foreign_key('export_access_logs_export_id_fkey', 'export_access_logs', 'data_exports', ['export_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_export_access_logs_id'), table_name='export_access_logs')
    op.create_index('idx_export_access_logs_export_id', 'export_access_logs', ['export_id'], unique=False)
    op.create_index('idx_export_access_logs_accessed_at', 'export_access_logs', ['accessed_at'], unique=False)
    op.create_index('idx_export_access_logs_access_type', 'export_access_logs', ['access_type'], unique=False)
    op.alter_column('export_access_logs', 'accessed_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('export_access_logs', 'bytes_transferred',
               existing_type=sa.Integer(),
               type_=sa.BIGINT(),
               existing_nullable=True)
    op.alter_column('export_access_logs', 'ip_address',
               existing_type=sa.String(length=45),
               type_=postgresql.INET(),
               existing_nullable=True)
    op.drop_column('export_access_logs', 'updated_by')
    op.drop_column('export_access_logs', 'updated_at')
    op.drop_column('export_access_logs', 'created_by')
    op.drop_column('export_access_logs', 'created_at')
    op.drop_constraint(None, 'data_inputs', type_='foreignkey')
    op.create_foreign_key('data_inputs_brand_id_fkey', 'data_inputs', 'brands', ['brand_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_data_inputs_id'), table_name='data_inputs')
    op.create_index('idx_data_inputs_is_deleted', 'data_inputs', ['is_deleted'], unique=False)
    op.alter_column('data_inputs', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               comment='Flag indicating if the record is soft deleted',
               existing_nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('data_inputs', 'deleted_by',
               existing_type=sa.VARCHAR(length=255),
               comment='Username of the user who soft deleted the record',
               existing_nullable=True)
    op.alter_column('data_inputs', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               comment='Timestamp when the record was soft deleted',
               existing_nullable=True)
    op.drop_constraint(None, 'data_input_pending_requests', type_='foreignkey')
    op.create_foreign_key('data_input_pending_requests_brand_id_fkey', 'data_input_pending_requests', 'brands', ['brand_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_data_input_pending_requests_id'), table_name='data_input_pending_requests')
    op.add_column('data_input_audit_logs', sa.Column('metadata', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.create_table_comment(
        'data_input_audit_logs',
        'Audit log for data input, attribute, and record operations',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'data_input_audit_logs', type_='foreignkey')
    op.drop_constraint(None, 'data_input_audit_logs', type_='foreignkey')
    op.drop_constraint(None, 'data_input_audit_logs', type_='foreignkey')
    op.create_foreign_key('data_input_audit_logs_user_id_fkey', 'data_input_audit_logs', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('data_input_audit_logs_brand_id_fkey', 'data_input_audit_logs', 'brands', ['brand_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('data_input_audit_logs_data_input_id_fkey', 'data_input_audit_logs', 'data_inputs', ['data_input_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_data_input_audit_logs_id'), table_name='data_input_audit_logs')
    op.create_index('idx_data_input_audit_logs_success', 'data_input_audit_logs', ['success'], unique=False)
    op.create_index('idx_data_input_audit_logs_severity', 'data_input_audit_logs', ['severity'], unique=False)
    op.create_index('idx_data_input_audit_logs_resource_type', 'data_input_audit_logs', ['resource_type'], unique=False)
    op.create_index('idx_data_input_audit_logs_brand_id', 'data_input_audit_logs', ['brand_id'], unique=False)
    op.alter_column('data_input_audit_logs', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('data_input_audit_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('data_input_audit_logs', 'severity',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('low', 'medium', 'high', 'critical', name='audit_severity'),
               existing_nullable=False,
               existing_server_default=sa.text("'medium'::audit_severity"))
    op.drop_column('data_input_audit_logs', 'audit_metadata')
    op.drop_constraint(None, 'data_input_attributes', type_='foreignkey')
    op.drop_constraint(None, 'data_input_attributes', type_='foreignkey')
    op.create_foreign_key('data_input_attributes_relationship_target_data_input_id_fkey', 'data_input_attributes', 'data_inputs', ['relationship_target_data_input_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('data_input_attributes_data_input_id_fkey', 'data_input_attributes', 'data_inputs', ['data_input_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_data_input_attributes_id'), table_name='data_input_attributes')
    op.create_index('idx_data_input_attributes_unique', 'data_input_attributes', ['is_unique'], unique=False)
    op.create_index('idx_data_input_attributes_required', 'data_input_attributes', ['is_required'], unique=False)
    op.create_index('idx_data_input_attributes_display_order', 'data_input_attributes', ['display_order'], unique=False)
    op.create_index('idx_data_input_attributes_data_type', 'data_input_attributes', ['data_type'], unique=False)
    op.create_unique_constraint('data_input_attributes_data_input_id_name_key', 'data_input_attributes', ['data_input_id', 'name'])
    op.alter_column('data_input_attributes', 'data_type',
               existing_type=sa.String(length=50),
               type_=postgresql.ENUM('Text', 'Number', 'Decimal', 'Boolean', 'Date', 'Datetime', 'File', 'Image', 'Video', 'Audio', 'URL', 'Email', name='data_type_enum'),
               existing_nullable=False,
               existing_server_default=sa.text("'Text'::data_type_enum"))
    op.drop_constraint(None, 'data_input_attribute_pending_requests', type_='foreignkey')
    op.drop_constraint(None, 'data_input_attribute_pending_requests', type_='foreignkey')
    op.drop_constraint(None, 'data_input_attribute_pending_requests', type_='foreignkey')
    op.create_foreign_key('data_input_attribute_pending__data_input_pending_request_i_fkey', 'data_input_attribute_pending_requests', 'data_input_pending_requests', ['data_input_pending_request_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('data_input_attribute_pending__relationship_target_data_inp_fkey', 'data_input_attribute_pending_requests', 'data_inputs', ['relationship_target_data_input_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('data_input_attribute_pending_requests_data_input_id_fkey', 'data_input_attribute_pending_requests', 'data_inputs', ['data_input_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_data_input_attribute_pending_requests_id'), table_name='data_input_attribute_pending_requests')
    op.create_unique_constraint('data_input_attribute_pending_requests_data_input_id_name_key', 'data_input_attribute_pending_requests', ['data_input_id', 'name'])
    op.create_table_comment(
        'data_exports',
        'Tracks data export jobs with filtering and format options',
        existing_comment=None,
        schema=None
    )
    op.drop_constraint(None, 'data_exports', type_='foreignkey')
    op.drop_constraint(None, 'data_exports', type_='foreignkey')
    op.drop_constraint(None, 'data_exports', type_='foreignkey')
    op.create_foreign_key('data_exports_data_input_id_fkey', 'data_exports', 'data_inputs', ['data_input_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('data_exports_brand_id_fkey', 'data_exports', 'brands', ['brand_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('data_exports_file_id_fkey', 'data_exports', 'files', ['file_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_data_exports_id'), table_name='data_exports')
    op.drop_index(op.f('ix_data_exports_export_id'), table_name='data_exports')
    op.create_index('idx_data_exports_status', 'data_exports', ['status'], unique=False)
    op.create_index('idx_data_exports_requested_by', 'data_exports', ['requested_by'], unique=False)
    op.create_index('idx_data_exports_export_id', 'data_exports', ['export_id'], unique=False)
    op.create_index('idx_data_exports_expires_at', 'data_exports', ['expires_at'], unique=False)
    op.create_index('idx_data_exports_data_input_id', 'data_exports', ['data_input_id'], unique=False)
    op.create_index('idx_data_exports_created_at', 'data_exports', ['created_at'], unique=False)
    op.create_index('idx_data_exports_brand_id', 'data_exports', ['brand_id'], unique=False)
    op.create_unique_constraint('data_exports_export_id_key', 'data_exports', ['export_id'])
    op.alter_column('data_exports', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('data_exports', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('data_exports', 'export_options',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='Format-specific export options',
               existing_nullable=True)
    op.alter_column('data_exports', 'file_size',
               existing_type=sa.Integer(),
               type_=sa.BIGINT(),
               existing_nullable=True)
    op.alter_column('data_exports', 'status',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', name='export_status'),
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::export_status"))
    op.alter_column('data_exports', 'filter_config',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='JSON configuration of applied filters',
               existing_nullable=True)
    op.alter_column('data_exports', 'selected_columns',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='Array of column names to include in export',
               existing_nullable=True)
    op.alter_column('data_exports', 'export_format',
               existing_type=sa.String(length=20),
               type_=postgresql.ENUM('csv', 'xlsx', 'json', 'pdf', name='export_format'),
               existing_nullable=False)
    op.alter_column('data_exports', 'export_type',
               existing_type=sa.String(length=50),
               type_=postgresql.ENUM('records', 'statistics', 'template', name='export_type'),
               existing_nullable=False,
               existing_server_default=sa.text("'records'::export_type"))
    op.alter_column('data_exports', 'export_id',
               existing_type=sa.VARCHAR(length=64),
               comment='Public UUID for referencing exports',
               existing_nullable=False)
    op.drop_index(op.f('ix_brands_id'), table_name='brands')
    op.drop_index(op.f('ix_audit_log_id'), table_name='audit_log')
    op.create_table('brand_access_audit',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('brand_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('action', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('old_role', postgresql.ENUM('viewer', 'editor', 'admin', 'owner', name='brand_role'), autoincrement=False, nullable=True),
    sa.Column('new_role', postgresql.ENUM('viewer', 'editor', 'admin', 'owner', name='brand_role'), autoincrement=False, nullable=True),
    sa.Column('old_permissions', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('new_permissions', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('performed_by', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('performed_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('reason', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['brand_id'], ['brands.id'], name='brand_access_audit_brand_id_fkey'),
    sa.ForeignKeyConstraint(['performed_by'], ['users.id'], name='brand_access_audit_performed_by_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='brand_access_audit_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='brand_access_audit_pkey')
    )
    op.create_index('idx_brand_access_audit_user', 'brand_access_audit', ['user_id'], unique=False)
    op.create_index('idx_brand_access_audit_performed_at', 'brand_access_audit', ['performed_at'], unique=False)
    op.create_index('idx_brand_access_audit_brand', 'brand_access_audit', ['brand_id'], unique=False)
    op.create_index('idx_brand_access_audit_action', 'brand_access_audit', ['action'], unique=False)
    # ### end Alembic commands ###
