FROM python:3.12-slim

# LABEL org.opencontainers.image.source="https://github.com/Seven-Retail-Group/SR-Internal-Service"

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Install PostgreSQL client for database connectivity checks
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .

RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY ./app /app/app

# Copy Alembic configuration and migrations
COPY ./alembic.ini /app/
COPY ./alembic /app/alembic

# Copy scripts directory for admin user creation
COPY ./scripts /app/scripts

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]