INSERT INTO public.data_input_pending_requests VALUES (1, 1, 'Competitor Price Monitoring', 'A weekly log to track the prices of key treatments from local competitors.', 'PENDING', 'Marketing team needs this to stay competitive on pricing strategies.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (2, 1, 'Practitioner Certification Tracking', 'A form to track the active certifications and license renewal dates for our practitioners.', 'PENDING', 'HR requires this for compliance and to ensure all staff are properly certified.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (3, 2, 'Patient Referral Program Tracker', 'Tracks which existing patients refer new patients to claim referral bonuses.', 'PENDING', 'This is needed to support the new marketing initiative launching next quarter.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (4, 2, 'External Dental Lab Work Order', 'A form to formally submit and track work orders sent to external labs for crowns, etc.', 'PENDING', 'Operations needs to standardize the lab ordering process to reduce errors.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (5, 3, 'Partnership & Affiliate Agreement Log', 'A central log to manage agreements with our training partners and affiliates.', 'PENDING', 'The business development team needs a single source of truth for all partnership contracts.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (6, 3, 'E-learning Content Development Pipeline', 'Tracks the progress of new e-learning modules from ideation to launch.', 'PENDING', 'The content team wants to formalize their development workflow.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (7, 4, 'Third-Party Delivery Platform Performance', 'Daily tracking of sales, commissions, and issues with GoFood, GrabFood, etc.', 'PENDING', 'Finance needs better data to analyze the profitability of our delivery partners.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);
INSERT INTO public.data_input_pending_requests VALUES (8, 4, 'Supplier Quality Audit Form', 'A structured checklist for conducting and scoring quality audits of our key suppliers.', 'PENDING', 'The supply chain team is implementing a new supplier verification process.', '2025-06-12 17:47:43.928877+07', 'system_setup', '2025-06-12 17:47:43.928877+07', 'system_setup', NULL);


