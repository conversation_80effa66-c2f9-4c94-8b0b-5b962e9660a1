--
-- NOTE:
--
-- File paths need to be edited. Search for $$PATH$$ and
-- replace it with the path to the directory containing
-- the extracted data files.
--
--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Homebrew)
-- Dumped by pg_dump version 17.5 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP DATABASE internal_service;
--
-- Name: internal_service; Type: DATABASE; Schema: -; Owner: ipman
--

CREATE DATABASE internal_service WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'C';


ALTER DATABASE internal_service OWNER TO ipman;

\connect internal_service

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: audit_action; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.audit_action AS ENUM (
    'create',
    'read',
    'update',
    'delete',
    'login',
    'logout',
    'login_failed',
    'password_change',
    'password_reset',
    'access_granted',
    'access_denied',
    'permission_change',
    'role_change',
    'submit',
    'approve',
    'reject',
    'cancel',
    'reopen',
    'upload',
    'download',
    'share',
    'export',
    'backup',
    'restore',
    'maintenance'
);


ALTER TYPE public.audit_action OWNER TO ipman;

--
-- Name: audit_severity; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.audit_severity AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


ALTER TYPE public.audit_severity OWNER TO ipman;

--
-- Name: brand_role; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.brand_role AS ENUM (
    'viewer',
    'editor',
    'admin',
    'owner'
);


ALTER TYPE public.brand_role OWNER TO ipman;

--
-- Name: data_type_enum; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.data_type_enum AS ENUM (
    'Text',
    'Number',
    'Decimal',
    'Boolean',
    'Date',
    'Datetime',
    'File',
    'Image',
    'Video',
    'Audio',
    'URL',
    'Email'
);


ALTER TYPE public.data_type_enum OWNER TO ipman;

--
-- Name: datatypeenum; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.datatypeenum AS ENUM (
    'TEXT',
    'NUMBER',
    'DECIMAL',
    'BOOLEAN',
    'DATE',
    'DATETIME',
    'FILE',
    'IMAGE',
    'VIDEO',
    'AUDIO',
    'URL',
    'EMAIL'
);


ALTER TYPE public.datatypeenum OWNER TO ipman;

--
-- Name: delivery_method; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.delivery_method AS ENUM (
    'download',
    'email',
    'ftp',
    's3'
);


ALTER TYPE public.delivery_method OWNER TO ipman;

--
-- Name: export_format; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.export_format AS ENUM (
    'csv',
    'xlsx',
    'json',
    'pdf'
);


ALTER TYPE public.export_format OWNER TO ipman;

--
-- Name: export_status; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.export_status AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed',
    'cancelled'
);


ALTER TYPE public.export_status OWNER TO ipman;

--
-- Name: export_type; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.export_type AS ENUM (
    'records',
    'statistics',
    'template'
);


ALTER TYPE public.export_type OWNER TO ipman;

--
-- Name: file_status; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.file_status AS ENUM (
    'uploading',
    'processing',
    'ready',
    'error',
    'quarantined',
    'deleted'
);


ALTER TYPE public.file_status OWNER TO ipman;

--
-- Name: file_type; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.file_type AS ENUM (
    'image',
    'document',
    'audio',
    'video',
    'archive',
    'other'
);


ALTER TYPE public.file_type OWNER TO ipman;

--
-- Name: request_priority; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.request_priority AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


ALTER TYPE public.request_priority OWNER TO ipman;

--
-- Name: request_status; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.request_status AS ENUM (
    'pending',
    'under_review',
    'approved',
    'rejected',
    'cancelled',
    'implemented'
);


ALTER TYPE public.request_status OWNER TO ipman;

--
-- Name: request_type; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.request_type AS ENUM (
    'create_data_input',
    'update_data_input',
    'delete_data_input',
    'create_attribute',
    'update_attribute',
    'delete_attribute',
    'create_brand',
    'update_brand',
    'delete_brand'
);


ALTER TYPE public.request_type OWNER TO ipman;

--
-- Name: schedule_type; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.schedule_type AS ENUM (
    'daily',
    'weekly',
    'monthly',
    'custom'
);


ALTER TYPE public.schedule_type OWNER TO ipman;

--
-- Name: storage_backend; Type: TYPE; Schema: public; Owner: ipman
--

CREATE TYPE public.storage_backend AS ENUM (
    'local',
    's3',
    'gcs'
);


ALTER TYPE public.storage_backend OWNER TO ipman;

--
-- Name: check_brand_permission(integer, integer, character varying); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.check_brand_permission(user_id_param integer, brand_id_param integer, permission_name character varying) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    user_brand_record user_brands%ROWTYPE;
    has_permission BOOLEAN := false;
BEGIN
    -- Get user brand record
    SELECT * INTO user_brand_record
    FROM user_brands 
    WHERE user_id = user_id_param 
    AND brand_id = brand_id_param 
    AND is_active = true;
    
    -- If no active access found, return false
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    -- Check specific permission
    CASE permission_name
        WHEN 'view' THEN
            has_permission := user_brand_record.can_view;
        WHEN 'create' THEN
            has_permission := user_brand_record.can_create;
        WHEN 'edit' THEN
            has_permission := user_brand_record.can_edit;
        WHEN 'delete' THEN
            has_permission := user_brand_record.can_delete;
        WHEN 'manage_users' THEN
            has_permission := user_brand_record.can_manage_users;
        WHEN 'approve_requests' THEN
            has_permission := user_brand_record.can_approve_requests;
        ELSE
            has_permission := false;
    END CASE;
    
    RETURN has_permission;
END;
$$;


ALTER FUNCTION public.check_brand_permission(user_id_param integer, brand_id_param integer, permission_name character varying) OWNER TO ipman;

--
-- Name: cleanup_audit_logs(character varying, integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.cleanup_audit_logs(p_log_type character varying, p_retention_days integer DEFAULT 365) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    cleanup_count INTEGER := 0;
    cutoff_date TIMESTAMP := CURRENT_TIMESTAMP - (p_retention_days || ' days')::INTERVAL;
BEGIN
    IF p_log_type = 'user' THEN
        DELETE FROM user_audit_logs WHERE created_at < cutoff_date;
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    ELSIF p_log_type = 'data_input' THEN
        DELETE FROM data_input_audit_logs WHERE created_at < cutoff_date;
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    ELSIF p_log_type = 'workflow' THEN
        DELETE FROM workflow_audit_logs WHERE created_at < cutoff_date;
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    ELSIF p_log_type = 'security' THEN
        DELETE FROM security_audit_logs WHERE created_at < cutoff_date;
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    ELSIF p_log_type = 'system' THEN
        DELETE FROM system_audit_logs WHERE created_at < cutoff_date;
        GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    END IF;
    
    RETURN cleanup_count;
END;
$$;


ALTER FUNCTION public.cleanup_audit_logs(p_log_type character varying, p_retention_days integer) OWNER TO ipman;

--
-- Name: cleanup_expired_export_shares(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.cleanup_expired_export_shares() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    cleanup_count INTEGER := 0;
BEGIN
    -- Deactivate expired shares
    UPDATE export_shares 
    SET is_active = FALSE 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND is_active = TRUE;
    
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RETURN cleanup_count;
END;
$$;


ALTER FUNCTION public.cleanup_expired_export_shares() OWNER TO ipman;

--
-- Name: cleanup_expired_exports(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.cleanup_expired_exports() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    cleanup_count INTEGER := 0;
    export_record RECORD;
BEGIN
    -- Get expired exports
    FOR export_record IN 
        SELECT id, file_id FROM data_exports 
        WHERE expires_at < CURRENT_TIMESTAMP 
        AND status IN ('completed', 'failed')
    LOOP
        -- Delete associated file if exists
        IF export_record.file_id IS NOT NULL THEN
            UPDATE files SET is_deleted = TRUE, deleted_at = CURRENT_TIMESTAMP, deleted_by = 'system'
            WHERE id = export_record.file_id;
        END IF;
        
        -- Delete export record
        DELETE FROM data_exports WHERE id = export_record.id;
        cleanup_count := cleanup_count + 1;
    END LOOP;
    
    RETURN cleanup_count;
END;
$$;


ALTER FUNCTION public.cleanup_expired_exports() OWNER TO ipman;

--
-- Name: cleanup_expired_file_shares(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.cleanup_expired_file_shares() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE file_shares 
    SET is_active = FALSE 
    WHERE expires_at <= CURRENT_TIMESTAMP 
    AND is_active = TRUE;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$;


ALTER FUNCTION public.cleanup_expired_file_shares() OWNER TO ipman;

--
-- Name: get_audit_statistics(character varying, integer, integer, integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_audit_statistics(p_log_type character varying DEFAULT NULL::character varying, p_user_id integer DEFAULT NULL::integer, p_brand_id integer DEFAULT NULL::integer, p_days integer DEFAULT 30) RETURNS TABLE(log_type character varying, total_logs bigint, actions jsonb, severity_levels jsonb, success_count bigint, failure_count bigint, recent_activity_24h bigint)
    LANGUAGE plpgsql
    AS $$
DECLARE
    start_date TIMESTAMP := CURRENT_TIMESTAMP - (p_days || ' days')::INTERVAL;
    recent_start TIMESTAMP := CURRENT_TIMESTAMP - '24 hours'::INTERVAL;
BEGIN
    -- User audit logs
    IF p_log_type IS NULL OR p_log_type = 'user' THEN
        SELECT 
            'user',
            COUNT(*),
            jsonb_object_agg(action, action_count),
            jsonb_object_agg(severity, severity_count),
            SUM(CASE WHEN success THEN 1 ELSE 0 END),
            SUM(CASE WHEN NOT success THEN 1 ELSE 0 END),
            COUNT(*) FILTER (WHERE created_at >= recent_start)
        INTO log_type, total_logs, actions, severity_levels, success_count, failure_count, recent_activity_24h
        FROM (
            SELECT 
                action,
                severity,
                success,
                created_at,
                COUNT(*) OVER (PARTITION BY action) as action_count,
                COUNT(*) OVER (PARTITION BY severity) as severity_count
            FROM user_audit_logs
            WHERE created_at >= start_date
            AND (p_user_id IS NULL OR user_id = p_user_id)
        ) subq;
        
        RETURN NEXT;
    END IF;
    
    -- Similar blocks for other log types would go here
    -- (Simplified for space - full implementation would include all log types)
    
END;
$$;


ALTER FUNCTION public.get_audit_statistics(p_log_type character varying, p_user_id integer, p_brand_id integer, p_days integer) OWNER TO ipman;

--
-- Name: get_brand_access_stats(integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_brand_access_stats(brand_id_param integer) RETURNS TABLE(total_users bigint, active_users bigint, viewers bigint, editors bigint, admins bigint, owners bigint)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM user_brands WHERE brand_id = brand_id_param) as total_users,
        (SELECT COUNT(*) FROM user_brands WHERE brand_id = brand_id_param AND is_active = true) as active_users,
        (SELECT COUNT(*) FROM user_brands WHERE brand_id = brand_id_param AND brand_role = 'viewer' AND is_active = true) as viewers,
        (SELECT COUNT(*) FROM user_brands WHERE brand_id = brand_id_param AND brand_role = 'editor' AND is_active = true) as editors,
        (SELECT COUNT(*) FROM user_brands WHERE brand_id = brand_id_param AND brand_role = 'admin' AND is_active = true) as admins,
        (SELECT COUNT(*) FROM user_brands WHERE brand_id = brand_id_param AND brand_role = 'owner' AND is_active = true) as owners;
END;
$$;


ALTER FUNCTION public.get_brand_access_stats(brand_id_param integer) OWNER TO ipman;

--
-- Name: get_export_statistics(integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_export_statistics(p_brand_id integer DEFAULT NULL::integer) RETURNS TABLE(total_exports bigint, exports_by_status jsonb, exports_by_format jsonb, recent_exports bigint, total_size_bytes bigint)
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Get total exports
    SELECT COUNT(*) INTO total_exports
    FROM data_exports
    WHERE (p_brand_id IS NULL OR brand_id = p_brand_id);
    
    -- Get exports by status
    SELECT jsonb_object_agg(status, count) INTO exports_by_status
    FROM (
        SELECT status, COUNT(*) as count
        FROM data_exports
        WHERE (p_brand_id IS NULL OR brand_id = p_brand_id)
        GROUP BY status
    ) status_counts;
    
    -- Get exports by format
    SELECT jsonb_object_agg(export_format, count) INTO exports_by_format
    FROM (
        SELECT export_format, COUNT(*) as count
        FROM data_exports
        WHERE (p_brand_id IS NULL OR brand_id = p_brand_id)
        GROUP BY export_format
    ) format_counts;
    
    -- Get recent exports (last 30 days)
    SELECT COUNT(*) INTO recent_exports
    FROM data_exports
    WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '30 days'
    AND (p_brand_id IS NULL OR brand_id = p_brand_id);
    
    -- Get total file size
    SELECT COALESCE(SUM(file_size), 0) INTO total_size_bytes
    FROM data_exports
    WHERE file_size IS NOT NULL
    AND (p_brand_id IS NULL OR brand_id = p_brand_id);
    
    RETURN QUERY SELECT 
        total_exports,
        exports_by_status,
        exports_by_format,
        recent_exports,
        total_size_bytes;
END;
$$;


ALTER FUNCTION public.get_export_statistics(p_brand_id integer) OWNER TO ipman;

--
-- Name: get_file_storage_stats(integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_file_storage_stats(brand_id_param integer DEFAULT NULL::integer) RETURNS TABLE(total_files bigint, total_size_bytes bigint, files_by_type jsonb, files_by_status jsonb, recent_uploads_24h bigint, storage_usage_by_backend jsonb)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    WITH file_stats AS (
        SELECT 
            COUNT(*) as total_files,
            SUM(file_size) as total_size_bytes,
            jsonb_object_agg(file_type, type_count) as files_by_type,
            jsonb_object_agg(status, status_count) as files_by_status,
            SUM(CASE WHEN created_at >= CURRENT_TIMESTAMP - INTERVAL '24 hours' THEN 1 ELSE 0 END) as recent_uploads_24h,
            jsonb_object_agg(storage_backend, backend_size) as storage_usage_by_backend
        FROM (
            SELECT 
                file_type,
                status,
                storage_backend,
                file_size,
                created_at,
                COUNT(*) OVER (PARTITION BY file_type) as type_count,
                COUNT(*) OVER (PARTITION BY status) as status_count,
                SUM(file_size) OVER (PARTITION BY storage_backend) as backend_size
            FROM files 
            WHERE is_deleted = FALSE
            AND (brand_id_param IS NULL OR brand_id = brand_id_param)
        ) grouped
        GROUP BY ()
    )
    SELECT * FROM file_stats;
END;
$$;


ALTER FUNCTION public.get_file_storage_stats(brand_id_param integer) OWNER TO ipman;

--
-- Name: get_user_brand_stats(integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_user_brand_stats(user_id_param integer) RETURNS TABLE(total_brands bigint, active_brands bigint, viewer_brands bigint, editor_brands bigint, admin_brands bigint, owner_brands bigint)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM user_brands WHERE user_id = user_id_param) as total_brands,
        (SELECT COUNT(*) FROM user_brands WHERE user_id = user_id_param AND is_active = true) as active_brands,
        (SELECT COUNT(*) FROM user_brands WHERE user_id = user_id_param AND brand_role = 'viewer' AND is_active = true) as viewer_brands,
        (SELECT COUNT(*) FROM user_brands WHERE user_id = user_id_param AND brand_role = 'editor' AND is_active = true) as editor_brands,
        (SELECT COUNT(*) FROM user_brands WHERE user_id = user_id_param AND brand_role = 'admin' AND is_active = true) as admin_brands,
        (SELECT COUNT(*) FROM user_brands WHERE user_id = user_id_param AND brand_role = 'owner' AND is_active = true) as owner_brands;
END;
$$;


ALTER FUNCTION public.get_user_brand_stats(user_id_param integer) OWNER TO ipman;

--
-- Name: get_validation_schema(integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_validation_schema(data_input_id_param integer) RETURNS TABLE(attribute_name character varying, attribute_label character varying, data_type character varying, is_required boolean, is_unique boolean, min_length integer, max_length integer, min_value character varying, max_value character varying, pattern character varying, allowed_values jsonb, default_value text, help_text text, placeholder character varying, display_order integer)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dia.name,
        dia.label,
        dia.data_type,
        dia.is_required,
        dia.is_unique,
        dia.min_length,
        dia.max_length,
        dia.min_value,
        dia.max_value,
        dia.pattern,
        dia.allowed_values,
        dia.default_value,
        dia.help_text,
        dia.placeholder,
        dia.display_order
    FROM data_input_attributes dia
    WHERE dia.data_input_id = data_input_id_param
    AND dia.is_active = true
    ORDER BY dia.display_order, dia.name;
END;
$$;


ALTER FUNCTION public.get_validation_schema(data_input_id_param integer) OWNER TO ipman;

--
-- Name: get_workflow_stats(integer); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.get_workflow_stats(user_id_param integer DEFAULT NULL::integer) RETURNS TABLE(total_requests bigint, pending_requests bigint, under_review_requests bigint, approved_requests bigint, rejected_requests bigint, implemented_requests bigint, my_pending_requests bigint, my_pending_reviews bigint, my_pending_approvals bigint)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM workflow_requests) as total_requests,
        (SELECT COUNT(*) FROM workflow_requests WHERE status = 'pending') as pending_requests,
        (SELECT COUNT(*) FROM workflow_requests WHERE status = 'under_review') as under_review_requests,
        (SELECT COUNT(*) FROM workflow_requests WHERE status = 'approved') as approved_requests,
        (SELECT COUNT(*) FROM workflow_requests WHERE status = 'rejected') as rejected_requests,
        (SELECT COUNT(*) FROM workflow_requests WHERE status = 'implemented') as implemented_requests,
        CASE 
            WHEN user_id_param IS NOT NULL THEN 
                (SELECT COUNT(*) FROM workflow_requests 
                 WHERE requester_id = user_id_param 
                 AND status IN ('pending', 'under_review'))
            ELSE 0
        END as my_pending_requests,
        CASE 
            WHEN user_id_param IS NOT NULL THEN 
                (SELECT COUNT(*) FROM workflow_requests 
                 WHERE reviewer_id = user_id_param 
                 AND status = 'under_review')
            ELSE 0
        END as my_pending_reviews,
        CASE 
            WHEN user_id_param IS NOT NULL THEN 
                (SELECT COUNT(*) FROM workflow_requests 
                 WHERE approver_id = user_id_param 
                 AND status = 'under_review')
            ELSE 0
        END as my_pending_approvals;
END;
$$;


ALTER FUNCTION public.get_workflow_stats(user_id_param integer) OWNER TO ipman;

--
-- Name: log_brand_access_changes(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.log_brand_access_changes() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    action_type VARCHAR(50);
    old_perms JSONB;
    new_perms JSONB;
BEGIN
    -- Determine action type
    IF TG_OP = 'INSERT' THEN
        action_type := 'granted';
        old_perms := NULL;
        new_perms := jsonb_build_object(
            'can_view', NEW.can_view,
            'can_create', NEW.can_create,
            'can_edit', NEW.can_edit,
            'can_delete', NEW.can_delete,
            'can_manage_users', NEW.can_manage_users,
            'can_approve_requests', NEW.can_approve_requests
        );
    ELSIF TG_OP = 'UPDATE' THEN
        IF NEW.is_active = false AND OLD.is_active = true THEN
            action_type := 'revoked';
        ELSIF NEW.brand_role != OLD.brand_role THEN
            action_type := 'role_changed';
        ELSE
            action_type := 'updated';
        END IF;
        
        old_perms := jsonb_build_object(
            'can_view', OLD.can_view,
            'can_create', OLD.can_create,
            'can_edit', OLD.can_edit,
            'can_delete', OLD.can_delete,
            'can_manage_users', OLD.can_manage_users,
            'can_approve_requests', OLD.can_approve_requests
        );
        new_perms := jsonb_build_object(
            'can_view', NEW.can_view,
            'can_create', NEW.can_create,
            'can_edit', NEW.can_edit,
            'can_delete', NEW.can_delete,
            'can_manage_users', NEW.can_manage_users,
            'can_approve_requests', NEW.can_approve_requests
        );
    END IF;
    
    -- Insert audit record
    INSERT INTO brand_access_audit (
        user_id, brand_id, action, old_role, new_role,
        old_permissions, new_permissions, performed_by,
        created_by, updated_by
    ) VALUES (
        COALESCE(NEW.user_id, OLD.user_id),
        COALESCE(NEW.brand_id, OLD.brand_id),
        action_type,
        CASE WHEN TG_OP = 'UPDATE' THEN OLD.brand_role ELSE NULL END,
        CASE WHEN TG_OP != 'DELETE' THEN NEW.brand_role ELSE NULL END,
        old_perms,
        new_perms,
        1, -- Default to user ID 1, should be updated by application
        COALESCE(NEW.updated_by, OLD.updated_by, 'system'),
        COALESCE(NEW.updated_by, OLD.updated_by, 'system')
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$;


ALTER FUNCTION public.log_brand_access_changes() OWNER TO ipman;

--
-- Name: log_changes(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.log_changes() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_old_data jsonb;
    v_new_data jsonb;
BEGIN
    IF (TG_OP = 'UPDATE') THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := to_jsonb(NEW);
        INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, new_data, query)
        VALUES (TG_TABLE_SCHEMA::TEXT, TG_TABLE_NAME::TEXT, session_user::TEXT, 'U', v_old_data, v_new_data, current_query());
        RETURN NEW;
    ELSIF (TG_OP = 'DELETE') THEN
        v_old_data := to_jsonb(OLD);
        INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, query)
        VALUES (TG_TABLE_SCHEMA::TEXT, TG_TABLE_NAME::TEXT, session_user::TEXT, 'D', v_old_data, current_query());
        RETURN OLD;
    ELSIF (TG_OP = 'INSERT') THEN
        v_new_data := to_jsonb(NEW);
        INSERT INTO audit_log (schema_name, table_name, user_name, action, new_data, query)
        VALUES (TG_TABLE_SCHEMA::TEXT, TG_TABLE_NAME::TEXT, session_user::TEXT, 'I', v_new_data, current_query());
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.log_changes() OWNER TO ipman;

--
-- Name: set_permissions_for_role(public.brand_role); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.set_permissions_for_role(role public.brand_role) RETURNS TABLE(can_view boolean, can_create boolean, can_edit boolean, can_delete boolean, can_manage_users boolean, can_approve_requests boolean)
    LANGUAGE plpgsql
    AS $$
BEGIN
    CASE role
        WHEN 'viewer' THEN
            RETURN QUERY SELECT true, false, false, false, false, false;
        WHEN 'editor' THEN
            RETURN QUERY SELECT true, true, true, false, false, false;
        WHEN 'admin' THEN
            RETURN QUERY SELECT true, true, true, true, true, true;
        WHEN 'owner' THEN
            RETURN QUERY SELECT true, true, true, true, true, true;
        ELSE
            RETURN QUERY SELECT true, false, false, false, false, false;
    END CASE;
END;
$$;


ALTER FUNCTION public.set_permissions_for_role(role public.brand_role) OWNER TO ipman;

--
-- Name: update_permissions_on_role_change(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.update_permissions_on_role_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Only update permissions if role changed and no custom permissions were set
    IF NEW.brand_role != OLD.brand_role THEN
        SELECT * INTO NEW.can_view, NEW.can_create, NEW.can_edit, 
                     NEW.can_delete, NEW.can_manage_users, NEW.can_approve_requests
        FROM set_permissions_for_role(NEW.brand_role);
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_permissions_on_role_change() OWNER TO ipman;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO ipman;

--
-- Name: update_user_timestamps(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.update_user_timestamps() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
   NEW.updated_at = now();
   NEW.changed_on = now();
   RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_user_timestamps() OWNER TO ipman;

--
-- Name: validate_record_before_save(); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.validate_record_before_save() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    validation_result RECORD;
BEGIN
    -- Only validate if value is not null
    IF NEW.value IS NOT NULL THEN
        SELECT * INTO validation_result 
        FROM validate_record_data(NEW.data_input_id, NEW.value);
        
        -- If validation fails, prevent the operation
        IF NOT validation_result.is_valid THEN
            RAISE EXCEPTION 'Data validation failed: %', validation_result.errors::TEXT;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.validate_record_before_save() OWNER TO ipman;

--
-- Name: validate_record_data(integer, jsonb); Type: FUNCTION; Schema: public; Owner: ipman
--

CREATE FUNCTION public.validate_record_data(data_input_id_param integer, record_data jsonb) RETURNS TABLE(is_valid boolean, errors jsonb, warnings jsonb)
    LANGUAGE plpgsql
    AS $_$
DECLARE
    attr_record RECORD;
    field_value TEXT;
    error_list JSONB := '[]'::JSONB;
    warning_list JSONB := '[]'::JSONB;
    is_data_valid BOOLEAN := true;
    allowed_vals JSONB;
    min_len INTEGER;
    max_len INTEGER;
    pattern_regex VARCHAR(500);
BEGIN
    -- Loop through all active attributes for the data input
    FOR attr_record IN 
        SELECT * FROM data_input_attributes 
        WHERE data_input_id = data_input_id_param 
        AND is_active = true
    LOOP
        -- Get field value from record data
        field_value := record_data ->> attr_record.name;
        
        -- Check required fields
        IF attr_record.is_required AND (field_value IS NULL OR field_value = '') THEN
            -- Check if there's a default value
            IF attr_record.default_value IS NOT NULL THEN
                -- Use default value (this would be handled by application)
                CONTINUE;
            ELSE
                error_list := error_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'required',
                    'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" is required',
                    'provided_value', field_value
                );
                is_data_valid := false;
                CONTINUE;
            END IF;
        END IF;
        
        -- Skip validation if field is empty and not required
        IF field_value IS NULL OR field_value = '' THEN
            CONTINUE;
        END IF;
        
        -- Text validation
        IF attr_record.data_type = 'Text' THEN
            -- Min length validation
            IF attr_record.min_length IS NOT NULL AND LENGTH(field_value) < attr_record.min_length THEN
                error_list := error_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'min_length',
                    'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be at least ' || attr_record.min_length || ' characters long',
                    'provided_value', field_value,
                    'expected_format', 'Minimum ' || attr_record.min_length || ' characters'
                );
                is_data_valid := false;
            END IF;
            
            -- Max length validation
            IF attr_record.max_length IS NOT NULL AND LENGTH(field_value) > attr_record.max_length THEN
                error_list := error_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'max_length',
                    'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be no more than ' || attr_record.max_length || ' characters long',
                    'provided_value', field_value,
                    'expected_format', 'Maximum ' || attr_record.max_length || ' characters'
                );
                is_data_valid := false;
            END IF;
            
            -- Pattern validation (basic - full regex would need extension)
            IF attr_record.pattern IS NOT NULL THEN
                -- Note: PostgreSQL regex validation would go here
                -- For now, we'll add a warning that pattern validation should be done in application
                warning_list := warning_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'pattern_check',
                    'error_message', 'Pattern validation should be performed by application layer',
                    'provided_value', field_value
                );
            END IF;
            
            -- Allowed values validation
            IF attr_record.allowed_values IS NOT NULL THEN
                IF NOT (attr_record.allowed_values ? field_value) THEN
                    error_list := error_list || jsonb_build_object(
                        'attribute_name', attr_record.name,
                        'attribute_label', COALESCE(attr_record.label, attr_record.name),
                        'error_type', 'allowed_values',
                        'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be one of the allowed values',
                        'provided_value', field_value,
                        'expected_format', 'Allowed values: ' || attr_record.allowed_values::TEXT
                    );
                    is_data_valid := false;
                END IF;
            END IF;
        END IF;
        
        -- Number validation
        IF attr_record.data_type IN ('Number', 'Decimal') THEN
            -- Check if value is numeric
            IF field_value !~ '^-?[0-9]+\.?[0-9]*$' THEN
                error_list := error_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'invalid_number',
                    'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be a valid number',
                    'provided_value', field_value,
                    'expected_format', 'Valid number (integer or decimal)'
                );
                is_data_valid := false;
            ELSE
                -- Min value validation
                IF attr_record.min_value IS NOT NULL AND field_value::NUMERIC < attr_record.min_value::NUMERIC THEN
                    error_list := error_list || jsonb_build_object(
                        'attribute_name', attr_record.name,
                        'attribute_label', COALESCE(attr_record.label, attr_record.name),
                        'error_type', 'min_value',
                        'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be at least ' || attr_record.min_value,
                        'provided_value', field_value,
                        'expected_format', 'Minimum value: ' || attr_record.min_value
                    );
                    is_data_valid := false;
                END IF;
                
                -- Max value validation
                IF attr_record.max_value IS NOT NULL AND field_value::NUMERIC > attr_record.max_value::NUMERIC THEN
                    error_list := error_list || jsonb_build_object(
                        'attribute_name', attr_record.name,
                        'attribute_label', COALESCE(attr_record.label, attr_record.name),
                        'error_type', 'max_value',
                        'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be no more than ' || attr_record.max_value,
                        'provided_value', field_value,
                        'expected_format', 'Maximum value: ' || attr_record.max_value
                    );
                    is_data_valid := false;
                END IF;
            END IF;
        END IF;
        
        -- Email validation (basic)
        IF attr_record.data_type = 'Email' THEN
            IF field_value !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
                error_list := error_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'invalid_email',
                    'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be a valid email address',
                    'provided_value', field_value,
                    'expected_format', 'Valid email format: <EMAIL>'
                );
                is_data_valid := false;
            END IF;
        END IF;
        
        -- Boolean validation
        IF attr_record.data_type = 'Boolean' THEN
            IF LOWER(field_value) NOT IN ('true', 'false', '1', '0', 'yes', 'no', 'on', 'off', 'y', 'n') THEN
                error_list := error_list || jsonb_build_object(
                    'attribute_name', attr_record.name,
                    'attribute_label', COALESCE(attr_record.label, attr_record.name),
                    'error_type', 'invalid_boolean',
                    'error_message', 'Field "' || COALESCE(attr_record.label, attr_record.name) || '" must be a valid boolean value',
                    'provided_value', field_value,
                    'expected_format', 'Valid values: true, false, 1, 0, yes, no, on, off'
                );
                is_data_valid := false;
            END IF;
        END IF;
        
    END LOOP;
    
    RETURN QUERY SELECT is_data_valid, error_list, warning_list;
END;
$_$;


ALTER FUNCTION public.validate_record_data(data_input_id_param integer, record_data jsonb) OWNER TO ipman;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO ipman;

--
-- Name: data_input_attributes; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.data_input_attributes (
    id integer NOT NULL,
    data_input_id integer NOT NULL,
    name character varying(100) NOT NULL,
    description character varying(255),
    data_type public.data_type_enum DEFAULT 'Text'::public.data_type_enum NOT NULL,
    is_required boolean DEFAULT false,
    is_unique boolean DEFAULT false,
    is_active boolean DEFAULT true,
    version integer DEFAULT 1,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    relationship_target_data_input_id integer,
    relationship_display_attribute character varying(100),
    label character varying(255),
    validation_rules jsonb,
    default_value text,
    min_length integer,
    max_length integer,
    min_value character varying(50),
    max_value character varying(50),
    pattern character varying(500),
    allowed_values jsonb,
    display_order integer DEFAULT 0,
    help_text text,
    placeholder character varying(255),
    format character varying(50)
);


ALTER TABLE public.data_input_attributes OWNER TO ipman;

--
-- Name: data_inputs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.data_inputs (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    brand_id integer NOT NULL,
    description character varying(255),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    deleted_at timestamp with time zone,
    deleted_by character varying(255),
    is_deleted boolean DEFAULT false NOT NULL,
    is_active boolean DEFAULT true NOT NULL
);


ALTER TABLE public.data_inputs OWNER TO ipman;

--
-- Name: COLUMN data_inputs.deleted_at; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_inputs.deleted_at IS 'Timestamp when the record was soft deleted';


--
-- Name: COLUMN data_inputs.deleted_by; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_inputs.deleted_by IS 'Username of the user who soft deleted the record';


--
-- Name: COLUMN data_inputs.is_deleted; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_inputs.is_deleted IS 'Flag indicating if the record is soft deleted';


--
-- Name: attribute_validation_summary; Type: VIEW; Schema: public; Owner: ipman
--

CREATE VIEW public.attribute_validation_summary AS
 SELECT dia.id,
    dia.data_input_id,
    di.name AS data_input_name,
    dia.name AS attribute_name,
    dia.label AS attribute_label,
    dia.data_type,
    dia.is_required,
    dia.is_unique,
    dia.is_active,
        CASE
            WHEN ((dia.min_length IS NOT NULL) OR (dia.max_length IS NOT NULL) OR (dia.min_value IS NOT NULL) OR (dia.max_value IS NOT NULL) OR (dia.pattern IS NOT NULL) OR (dia.allowed_values IS NOT NULL)) THEN true
            ELSE false
        END AS has_validation_rules,
        CASE
            WHEN (dia.default_value IS NOT NULL) THEN true
            ELSE false
        END AS has_default_value,
    dia.display_order,
    dia.created_at,
    dia.updated_at
   FROM (public.data_input_attributes dia
     JOIN public.data_inputs di ON ((dia.data_input_id = di.id)))
  WHERE (dia.is_active = true)
  ORDER BY di.name, dia.display_order, dia.name;


ALTER VIEW public.attribute_validation_summary OWNER TO ipman;

--
-- Name: audit_log; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.audit_log (
    id bigint NOT NULL,
    schema_name text NOT NULL,
    table_name text NOT NULL,
    user_name text,
    action_timestamp timestamp with time zone DEFAULT now() NOT NULL,
    action text NOT NULL,
    original_data jsonb,
    new_data jsonb,
    query text,
    CONSTRAINT audit_log_action_check CHECK ((action = ANY (ARRAY['I'::text, 'D'::text, 'U'::text])))
);


ALTER TABLE public.audit_log OWNER TO ipman;

--
-- Name: audit_log_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.audit_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.audit_log_id_seq OWNER TO ipman;

--
-- Name: audit_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.audit_log_id_seq OWNED BY public.audit_log.id;


--
-- Name: brand_access_audit; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.brand_access_audit (
    id integer NOT NULL,
    user_id integer NOT NULL,
    brand_id integer NOT NULL,
    action character varying(50) NOT NULL,
    old_role public.brand_role,
    new_role public.brand_role,
    old_permissions jsonb,
    new_permissions jsonb,
    performed_by integer NOT NULL,
    performed_at timestamp with time zone DEFAULT now() NOT NULL,
    reason text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.brand_access_audit OWNER TO ipman;

--
-- Name: brand_access_audit_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.brand_access_audit_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.brand_access_audit_id_seq OWNER TO ipman;

--
-- Name: brand_access_audit_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.brand_access_audit_id_seq OWNED BY public.brand_access_audit.id;


--
-- Name: brands; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.brands (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description character varying(255),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    is_active boolean DEFAULT true NOT NULL
);


ALTER TABLE public.brands OWNER TO ipman;

--
-- Name: user_brands; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.user_brands (
    id integer NOT NULL,
    user_id integer NOT NULL,
    brand_id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    brand_role public.brand_role DEFAULT 'viewer'::public.brand_role NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    can_view boolean DEFAULT true NOT NULL,
    can_create boolean DEFAULT false NOT NULL,
    can_edit boolean DEFAULT false NOT NULL,
    can_delete boolean DEFAULT false NOT NULL,
    can_manage_users boolean DEFAULT false NOT NULL,
    can_approve_requests boolean DEFAULT false NOT NULL
);


ALTER TABLE public.user_brands OWNER TO ipman;

--
-- Name: users; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.users (
    id integer NOT NULL,
    first_name character varying(64) NOT NULL,
    last_name character varying(64) NOT NULL,
    username character varying(64) NOT NULL,
    password character varying(256),
    email character varying(256) NOT NULL,
    active boolean,
    is_superuser boolean,
    last_login timestamp with time zone,
    login_count integer,
    fail_login_count integer,
    created_on timestamp with time zone DEFAULT now() NOT NULL,
    changed_on timestamp with time zone DEFAULT now() NOT NULL,
    created_by_fk integer,
    changed_by_fk integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255)
);


ALTER TABLE public.users OWNER TO ipman;

--
-- Name: brand_access_summary; Type: VIEW; Schema: public; Owner: ipman
--

CREATE VIEW public.brand_access_summary AS
 SELECT ub.id,
    ub.user_id,
    u.username,
    u.email,
    concat(u.first_name, ' ', u.last_name) AS full_name,
    ub.brand_id,
    b.name AS brand_name,
    b.description AS brand_description,
    ub.brand_role,
    ub.is_active,
    ub.can_view,
    ub.can_create,
    ub.can_edit,
    ub.can_delete,
    ub.can_manage_users,
    ub.can_approve_requests,
    ub.created_at AS granted_at,
    ub.updated_at AS last_updated
   FROM ((public.user_brands ub
     JOIN public.users u ON ((ub.user_id = u.id)))
     JOIN public.brands b ON ((ub.brand_id = b.id)));


ALTER VIEW public.brand_access_summary OWNER TO ipman;

--
-- Name: brands_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.brands_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.brands_id_seq OWNER TO ipman;

--
-- Name: brands_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.brands_id_seq OWNED BY public.brands.id;


--
-- Name: data_exports; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.data_exports (
    id integer NOT NULL,
    export_id character varying(64) NOT NULL,
    data_input_id integer NOT NULL,
    export_type public.export_type DEFAULT 'records'::public.export_type NOT NULL,
    export_format public.export_format NOT NULL,
    filename character varying(255),
    include_headers boolean DEFAULT true,
    selected_columns jsonb,
    filter_config jsonb,
    status public.export_status DEFAULT 'pending'::public.export_status NOT NULL,
    progress_percentage integer DEFAULT 0,
    total_records integer DEFAULT 0,
    processed_records integer DEFAULT 0,
    file_id integer,
    file_path character varying(500),
    file_size bigint,
    download_count integer DEFAULT 0,
    started_at timestamp without time zone,
    completed_at timestamp without time zone,
    expires_at timestamp without time zone,
    error_message text,
    error_details jsonb,
    requested_by character varying(255) NOT NULL,
    brand_id integer,
    export_options jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.data_exports OWNER TO ipman;

--
-- Name: TABLE data_exports; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.data_exports IS 'Tracks data export jobs with filtering and format options';


--
-- Name: COLUMN data_exports.export_id; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_exports.export_id IS 'Public UUID for referencing exports';


--
-- Name: COLUMN data_exports.selected_columns; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_exports.selected_columns IS 'Array of column names to include in export';


--
-- Name: COLUMN data_exports.filter_config; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_exports.filter_config IS 'JSON configuration of applied filters';


--
-- Name: COLUMN data_exports.export_options; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.data_exports.export_options IS 'Format-specific export options';


--
-- Name: data_exports_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.data_exports_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_exports_id_seq OWNER TO ipman;

--
-- Name: data_exports_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.data_exports_id_seq OWNED BY public.data_exports.id;


--
-- Name: data_input_attribute_pending_requests; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.data_input_attribute_pending_requests (
    id integer NOT NULL,
    data_input_id integer,
    name character varying(100) NOT NULL,
    description character varying(255),
    request_status character varying(50) NOT NULL,
    request_message character varying(255),
    data_type character varying(50) NOT NULL,
    is_required boolean DEFAULT false,
    is_unique boolean DEFAULT false,
    is_active boolean DEFAULT true,
    version integer DEFAULT 1,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    data_input_pending_request_id integer,
    relationship_target_data_input_id integer,
    relationship_display_attribute character varying(100),
    workflow_request_id integer
);


ALTER TABLE public.data_input_attribute_pending_requests OWNER TO ipman;

--
-- Name: data_input_attribute_pending_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.data_input_attribute_pending_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_input_attribute_pending_requests_id_seq OWNER TO ipman;

--
-- Name: data_input_attribute_pending_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.data_input_attribute_pending_requests_id_seq OWNED BY public.data_input_attribute_pending_requests.id;


--
-- Name: data_input_attributes_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.data_input_attributes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_input_attributes_id_seq OWNER TO ipman;

--
-- Name: data_input_attributes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.data_input_attributes_id_seq OWNED BY public.data_input_attributes.id;


--
-- Name: data_input_audit_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.data_input_audit_logs (
    id integer NOT NULL,
    user_id integer,
    data_input_id integer,
    action character varying(50) NOT NULL,
    severity public.audit_severity DEFAULT 'medium'::public.audit_severity NOT NULL,
    resource_type character varying(50) NOT NULL,
    resource_id character varying(100),
    operation_description text NOT NULL,
    old_values jsonb,
    new_values jsonb,
    changed_fields jsonb,
    ip_address inet,
    user_agent text,
    brand_id integer,
    success boolean DEFAULT true NOT NULL,
    error_message text,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.data_input_audit_logs OWNER TO ipman;

--
-- Name: TABLE data_input_audit_logs; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.data_input_audit_logs IS 'Audit log for data input, attribute, and record operations';


--
-- Name: data_input_audit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.data_input_audit_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_input_audit_logs_id_seq OWNER TO ipman;

--
-- Name: data_input_audit_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.data_input_audit_logs_id_seq OWNED BY public.data_input_audit_logs.id;


--
-- Name: data_input_pending_requests; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.data_input_pending_requests (
    id integer NOT NULL,
    brand_id integer NOT NULL,
    name character varying(100) NOT NULL,
    description character varying(255),
    request_status character varying(50) NOT NULL,
    request_message character varying(255),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    workflow_request_id integer
);


ALTER TABLE public.data_input_pending_requests OWNER TO ipman;

--
-- Name: data_input_pending_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.data_input_pending_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_input_pending_requests_id_seq OWNER TO ipman;

--
-- Name: data_input_pending_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.data_input_pending_requests_id_seq OWNED BY public.data_input_pending_requests.id;


--
-- Name: data_inputs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.data_inputs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.data_inputs_id_seq OWNER TO ipman;

--
-- Name: data_inputs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.data_inputs_id_seq OWNED BY public.data_inputs.id;


--
-- Name: export_access_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.export_access_logs (
    id integer NOT NULL,
    export_id integer NOT NULL,
    access_type character varying(50) NOT NULL,
    accessed_by character varying(255),
    ip_address inet,
    user_agent text,
    referer character varying(500),
    response_status integer,
    bytes_transferred bigint,
    access_duration integer,
    accessed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    access_metadata jsonb
);


ALTER TABLE public.export_access_logs OWNER TO ipman;

--
-- Name: TABLE export_access_logs; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.export_access_logs IS 'Audit log for export file access and downloads';


--
-- Name: export_access_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.export_access_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.export_access_logs_id_seq OWNER TO ipman;

--
-- Name: export_access_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.export_access_logs_id_seq OWNED BY public.export_access_logs.id;


--
-- Name: export_schedules; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.export_schedules (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    data_input_id integer NOT NULL,
    schedule_type public.schedule_type NOT NULL,
    schedule_expression character varying(100),
    timezone character varying(50) DEFAULT 'UTC'::character varying,
    export_format public.export_format NOT NULL,
    selected_columns jsonb,
    filter_config jsonb,
    export_options jsonb,
    is_active boolean DEFAULT true,
    last_run_at timestamp without time zone,
    next_run_at timestamp without time zone,
    run_count integer DEFAULT 0,
    delivery_method public.delivery_method DEFAULT 'download'::public.delivery_method,
    delivery_config jsonb,
    retention_days integer DEFAULT 30,
    max_file_count integer DEFAULT 10,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255) NOT NULL,
    updated_by character varying(255)
);


ALTER TABLE public.export_schedules OWNER TO ipman;

--
-- Name: TABLE export_schedules; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.export_schedules IS 'Scheduled/recurring export configurations';


--
-- Name: export_schedules_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.export_schedules_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.export_schedules_id_seq OWNER TO ipman;

--
-- Name: export_schedules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.export_schedules_id_seq OWNED BY public.export_schedules.id;


--
-- Name: export_shares; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.export_shares (
    id integer NOT NULL,
    export_id integer NOT NULL,
    share_token character varying(64) NOT NULL,
    expires_at timestamp without time zone,
    max_downloads integer,
    download_count integer DEFAULT 0,
    password_hash character varying(255),
    allowed_ips jsonb,
    require_auth boolean DEFAULT false,
    description text,
    is_active boolean DEFAULT true,
    created_by character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.export_shares OWNER TO ipman;

--
-- Name: TABLE export_shares; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.export_shares IS 'Secure sharing of export files with external users';


--
-- Name: export_shares_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.export_shares_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.export_shares_id_seq OWNER TO ipman;

--
-- Name: export_shares_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.export_shares_id_seq OWNED BY public.export_shares.id;


--
-- Name: export_templates; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.export_templates (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    data_input_id integer NOT NULL,
    export_format public.export_format NOT NULL,
    include_headers boolean DEFAULT true,
    selected_columns jsonb,
    default_filters jsonb,
    sort_config jsonb,
    is_public boolean DEFAULT false,
    is_active boolean DEFAULT true,
    usage_count integer DEFAULT 0,
    last_used_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255) NOT NULL,
    updated_by character varying(255)
);


ALTER TABLE public.export_templates OWNER TO ipman;

--
-- Name: TABLE export_templates; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.export_templates IS 'Reusable export configurations for common use cases';


--
-- Name: export_templates_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.export_templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.export_templates_id_seq OWNER TO ipman;

--
-- Name: export_templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.export_templates_id_seq OWNED BY public.export_templates.id;


--
-- Name: file_access_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.file_access_logs (
    id integer NOT NULL,
    file_id integer NOT NULL,
    accessed_by character varying(255),
    access_type character varying(20) NOT NULL,
    ip_address inet,
    user_agent character varying(500),
    request_method character varying(10),
    request_path character varying(500),
    response_status integer,
    bytes_transferred bigint,
    access_duration_ms integer,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.file_access_logs OWNER TO ipman;

--
-- Name: file_access_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.file_access_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.file_access_logs_id_seq OWNER TO ipman;

--
-- Name: file_access_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.file_access_logs_id_seq OWNED BY public.file_access_logs.id;


--
-- Name: file_processing_jobs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.file_processing_jobs (
    id integer NOT NULL,
    file_id integer NOT NULL,
    job_type character varying(50) NOT NULL,
    job_params jsonb,
    priority integer DEFAULT 0,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    started_at timestamp without time zone,
    completed_at timestamp without time zone,
    error_message text,
    output_files jsonb,
    processing_metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.file_processing_jobs OWNER TO ipman;

--
-- Name: file_processing_jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.file_processing_jobs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.file_processing_jobs_id_seq OWNER TO ipman;

--
-- Name: file_processing_jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.file_processing_jobs_id_seq OWNED BY public.file_processing_jobs.id;


--
-- Name: file_records; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.file_records (
    id integer NOT NULL,
    file_id integer NOT NULL,
    record_id integer NOT NULL,
    attribute_name character varying(100) NOT NULL,
    display_name character varying(255),
    description text,
    display_order integer DEFAULT 0,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.file_records OWNER TO ipman;

--
-- Name: file_records_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.file_records_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.file_records_id_seq OWNER TO ipman;

--
-- Name: file_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.file_records_id_seq OWNED BY public.file_records.id;


--
-- Name: file_shares; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.file_shares (
    id integer NOT NULL,
    file_id integer NOT NULL,
    share_token character varying(64) NOT NULL,
    expires_at timestamp without time zone,
    max_downloads integer,
    download_count integer DEFAULT 0,
    password_hash character varying(255),
    allowed_ips jsonb,
    require_auth boolean DEFAULT false,
    created_by character varying(255) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.file_shares OWNER TO ipman;

--
-- Name: file_shares_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.file_shares_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.file_shares_id_seq OWNER TO ipman;

--
-- Name: file_shares_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.file_shares_id_seq OWNED BY public.file_shares.id;


--
-- Name: file_summary; Type: VIEW; Schema: public; Owner: ipman
--

CREATE VIEW public.file_summary AS
SELECT
    NULL::integer AS id,
    NULL::character varying(255) AS original_filename,
    NULL::character varying(255) AS filename,
    NULL::bigint AS file_size,
    NULL::character varying(100) AS mime_type,
    NULL::public.file_type AS file_type,
    NULL::public.file_status AS status,
    NULL::character varying(255) AS uploaded_by,
    NULL::integer AS brand_id,
    NULL::character varying(255) AS brand_name,
    NULL::boolean AS is_public,
    NULL::integer AS download_count,
    NULL::boolean AS has_thumbnail,
    NULL::timestamp without time zone AS created_at,
    NULL::timestamp without time zone AS updated_at,
    NULL::bigint AS record_associations,
    NULL::bigint AS active_shares;


ALTER VIEW public.file_summary OWNER TO ipman;

--
-- Name: file_versions; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.file_versions (
    id integer NOT NULL,
    file_id integer NOT NULL,
    version_number integer NOT NULL,
    filename character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size bigint NOT NULL,
    checksum_md5 character varying(32),
    change_description text,
    replaced_by character varying(255) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.file_versions OWNER TO ipman;

--
-- Name: file_versions_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.file_versions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.file_versions_id_seq OWNER TO ipman;

--
-- Name: file_versions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.file_versions_id_seq OWNED BY public.file_versions.id;


--
-- Name: files; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.files (
    id integer NOT NULL,
    original_filename character varying(255) NOT NULL,
    filename character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size bigint NOT NULL,
    mime_type character varying(100) NOT NULL,
    file_extension character varying(10) NOT NULL,
    file_type public.file_type NOT NULL,
    status public.file_status DEFAULT 'uploading'::public.file_status NOT NULL,
    processing_error text,
    storage_backend public.storage_backend DEFAULT 'local'::public.storage_backend NOT NULL,
    storage_path character varying(500) NOT NULL,
    storage_metadata jsonb,
    checksum_md5 character varying(32),
    checksum_sha256 character varying(64),
    virus_scan_result character varying(20),
    virus_scan_date timestamp without time zone,
    image_width integer,
    image_height integer,
    image_format character varying(10),
    has_thumbnail boolean DEFAULT false,
    thumbnail_path character varying(500),
    duration_seconds integer,
    bitrate integer,
    uploaded_by character varying(255) NOT NULL,
    brand_id integer,
    is_public boolean DEFAULT false,
    access_permissions jsonb,
    download_count integer DEFAULT 0,
    last_accessed timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    deleted_at timestamp without time zone,
    deleted_by character varying(255),
    is_deleted boolean DEFAULT false
);


ALTER TABLE public.files OWNER TO ipman;

--
-- Name: files_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.files_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.files_id_seq OWNER TO ipman;

--
-- Name: files_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.files_id_seq OWNED BY public.files.id;


--
-- Name: records; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.records (
    id integer NOT NULL,
    data_input_id integer NOT NULL,
    value jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255),
    deleted_at timestamp with time zone,
    deleted_by character varying(255),
    is_deleted boolean DEFAULT false NOT NULL
);


ALTER TABLE public.records OWNER TO ipman;

--
-- Name: COLUMN records.deleted_at; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.records.deleted_at IS 'Timestamp when the record was soft deleted';


--
-- Name: COLUMN records.deleted_by; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.records.deleted_by IS 'Username of the user who soft deleted the record';


--
-- Name: COLUMN records.is_deleted; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.records.is_deleted IS 'Flag indicating if the record is soft deleted';


--
-- Name: records_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.records_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.records_id_seq OWNER TO ipman;

--
-- Name: records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.records_id_seq OWNED BY public.records.id;


--
-- Name: roles; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.roles (
    id integer NOT NULL,
    name character varying(64) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255)
);


ALTER TABLE public.roles OWNER TO ipman;

--
-- Name: roles_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.roles_id_seq OWNER TO ipman;

--
-- Name: roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.roles_id_seq OWNED BY public.roles.id;


--
-- Name: security_audit_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.security_audit_logs (
    id integer NOT NULL,
    user_id integer,
    action character varying(50) NOT NULL,
    severity public.audit_severity DEFAULT 'high'::public.audit_severity NOT NULL,
    event_type character varying(50) NOT NULL,
    event_category character varying(50) NOT NULL,
    operation_description text NOT NULL,
    ip_address inet,
    user_agent text,
    session_id character varying(255),
    attempted_resource character varying(500),
    username_attempted character varying(255),
    authentication_method character varying(50),
    required_permission character varying(100),
    granted_permissions jsonb,
    risk_score integer,
    threat_indicators jsonb,
    success boolean DEFAULT false NOT NULL,
    error_message text,
    response_status integer,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    CONSTRAINT security_audit_logs_risk_score_check CHECK (((risk_score >= 1) AND (risk_score <= 100)))
);


ALTER TABLE public.security_audit_logs OWNER TO ipman;

--
-- Name: TABLE security_audit_logs; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.security_audit_logs IS 'Audit log for security events, authentication, and authorization';


--
-- Name: COLUMN security_audit_logs.risk_score; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.security_audit_logs.risk_score IS 'Risk score from 1-100 based on threat assessment';


--
-- Name: COLUMN security_audit_logs.threat_indicators; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.security_audit_logs.threat_indicators IS 'Array of detected threat indicators';


--
-- Name: security_audit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.security_audit_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.security_audit_logs_id_seq OWNER TO ipman;

--
-- Name: security_audit_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.security_audit_logs_id_seq OWNED BY public.security_audit_logs.id;


--
-- Name: system_audit_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.system_audit_logs (
    id integer NOT NULL,
    user_id integer,
    action character varying(50) NOT NULL,
    severity public.audit_severity DEFAULT 'medium'::public.audit_severity NOT NULL,
    operation_type character varying(50) NOT NULL,
    component character varying(100) NOT NULL,
    operation_description text NOT NULL,
    configuration_key character varying(200),
    old_configuration jsonb,
    new_configuration jsonb,
    execution_time_ms integer,
    memory_usage_mb integer,
    cpu_usage_percent integer,
    success boolean DEFAULT true NOT NULL,
    error_message text,
    exit_code integer,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    CONSTRAINT system_audit_logs_cpu_usage_percent_check CHECK (((cpu_usage_percent >= 0) AND (cpu_usage_percent <= 100)))
);


ALTER TABLE public.system_audit_logs OWNER TO ipman;

--
-- Name: TABLE system_audit_logs; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.system_audit_logs IS 'Audit log for system-level operations and configuration changes';


--
-- Name: COLUMN system_audit_logs.execution_time_ms; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.system_audit_logs.execution_time_ms IS 'Operation execution time in milliseconds';


--
-- Name: system_audit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.system_audit_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.system_audit_logs_id_seq OWNER TO ipman;

--
-- Name: system_audit_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.system_audit_logs_id_seq OWNED BY public.system_audit_logs.id;


--
-- Name: user_audit_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.user_audit_logs (
    id integer NOT NULL,
    user_id integer,
    target_user_id integer,
    action character varying(50) NOT NULL,
    severity public.audit_severity DEFAULT 'medium'::public.audit_severity NOT NULL,
    resource_type character varying(50) NOT NULL,
    resource_id character varying(100),
    operation_description text NOT NULL,
    old_values jsonb,
    new_values jsonb,
    changed_fields jsonb,
    ip_address inet,
    user_agent text,
    session_id character varying(255),
    request_id character varying(100),
    success boolean DEFAULT true NOT NULL,
    error_message text,
    response_status integer,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.user_audit_logs OWNER TO ipman;

--
-- Name: TABLE user_audit_logs; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.user_audit_logs IS 'Audit log for user-related operations and changes';


--
-- Name: COLUMN user_audit_logs.changed_fields; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON COLUMN public.user_audit_logs.changed_fields IS 'Array of field names that were changed in the operation';


--
-- Name: user_audit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.user_audit_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_audit_logs_id_seq OWNER TO ipman;

--
-- Name: user_audit_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.user_audit_logs_id_seq OWNED BY public.user_audit_logs.id;


--
-- Name: user_brands_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.user_brands_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_brands_id_seq OWNER TO ipman;

--
-- Name: user_brands_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.user_brands_id_seq OWNED BY public.user_brands.id;


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.user_roles (
    id integer NOT NULL,
    user_id integer NOT NULL,
    role_id integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by character varying(255)
);


ALTER TABLE public.user_roles OWNER TO ipman;

--
-- Name: user_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.user_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_roles_id_seq OWNER TO ipman;

--
-- Name: user_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.user_roles_id_seq OWNED BY public.user_roles.id;


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO ipman;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: workflow_audit_logs; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.workflow_audit_logs (
    id integer NOT NULL,
    user_id integer,
    request_id integer,
    action character varying(50) NOT NULL,
    severity public.audit_severity DEFAULT 'medium'::public.audit_severity NOT NULL,
    resource_type character varying(50) NOT NULL,
    resource_id character varying(100),
    operation_description text NOT NULL,
    old_status character varying(50),
    new_status character varying(50),
    approval_level integer,
    comments text,
    old_values jsonb,
    new_values jsonb,
    ip_address inet,
    user_agent text,
    brand_id integer,
    success boolean DEFAULT true NOT NULL,
    error_message text,
    metadata jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.workflow_audit_logs OWNER TO ipman;

--
-- Name: TABLE workflow_audit_logs; Type: COMMENT; Schema: public; Owner: ipman
--

COMMENT ON TABLE public.workflow_audit_logs IS 'Audit log for workflow request operations and status changes';


--
-- Name: workflow_audit_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.workflow_audit_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.workflow_audit_logs_id_seq OWNER TO ipman;

--
-- Name: workflow_audit_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.workflow_audit_logs_id_seq OWNED BY public.workflow_audit_logs.id;


--
-- Name: workflow_requests; Type: TABLE; Schema: public; Owner: ipman
--

CREATE TABLE public.workflow_requests (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    request_type public.request_type NOT NULL,
    status public.request_status DEFAULT 'pending'::public.request_status NOT NULL,
    priority public.request_priority DEFAULT 'medium'::public.request_priority NOT NULL,
    request_data jsonb,
    requester_id integer NOT NULL,
    reviewer_id integer,
    approver_id integer,
    submitted_at timestamp with time zone,
    reviewed_at timestamp with time zone,
    approved_at timestamp with time zone,
    implemented_at timestamp with time zone,
    requester_comments text,
    reviewer_comments text,
    approver_comments text,
    rejection_reason text,
    brand_id integer,
    data_input_id integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(255),
    updated_by character varying(255)
);


ALTER TABLE public.workflow_requests OWNER TO ipman;

--
-- Name: workflow_request_summary; Type: VIEW; Schema: public; Owner: ipman
--

CREATE VIEW public.workflow_request_summary AS
 SELECT wr.id,
    wr.title,
    wr.request_type,
    wr.status,
    wr.priority,
    wr.requester_id,
    concat(u_req.first_name, ' ', u_req.last_name) AS requester_name,
    wr.reviewer_id,
    concat(u_rev.first_name, ' ', u_rev.last_name) AS reviewer_name,
    wr.approver_id,
    concat(u_app.first_name, ' ', u_app.last_name) AS approver_name,
    wr.brand_id,
    b.name AS brand_name,
    wr.data_input_id,
    di.name AS data_input_name,
    wr.submitted_at,
    wr.reviewed_at,
    wr.approved_at,
    wr.implemented_at,
    wr.created_at,
    wr.updated_at
   FROM (((((public.workflow_requests wr
     LEFT JOIN public.users u_req ON ((wr.requester_id = u_req.id)))
     LEFT JOIN public.users u_rev ON ((wr.reviewer_id = u_rev.id)))
     LEFT JOIN public.users u_app ON ((wr.approver_id = u_app.id)))
     LEFT JOIN public.brands b ON ((wr.brand_id = b.id)))
     LEFT JOIN public.data_inputs di ON ((wr.data_input_id = di.id)));


ALTER VIEW public.workflow_request_summary OWNER TO ipman;

--
-- Name: workflow_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: ipman
--

CREATE SEQUENCE public.workflow_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.workflow_requests_id_seq OWNER TO ipman;

--
-- Name: workflow_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: ipman
--

ALTER SEQUENCE public.workflow_requests_id_seq OWNED BY public.workflow_requests.id;


--
-- Name: audit_log id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.audit_log ALTER COLUMN id SET DEFAULT nextval('public.audit_log_id_seq'::regclass);


--
-- Name: brand_access_audit id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brand_access_audit ALTER COLUMN id SET DEFAULT nextval('public.brand_access_audit_id_seq'::regclass);


--
-- Name: brands id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brands ALTER COLUMN id SET DEFAULT nextval('public.brands_id_seq'::regclass);


--
-- Name: data_exports id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_exports ALTER COLUMN id SET DEFAULT nextval('public.data_exports_id_seq'::regclass);


--
-- Name: data_input_attribute_pending_requests id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests ALTER COLUMN id SET DEFAULT nextval('public.data_input_attribute_pending_requests_id_seq'::regclass);


--
-- Name: data_input_attributes id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attributes ALTER COLUMN id SET DEFAULT nextval('public.data_input_attributes_id_seq'::regclass);


--
-- Name: data_input_audit_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_audit_logs ALTER COLUMN id SET DEFAULT nextval('public.data_input_audit_logs_id_seq'::regclass);


--
-- Name: data_input_pending_requests id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_pending_requests ALTER COLUMN id SET DEFAULT nextval('public.data_input_pending_requests_id_seq'::regclass);


--
-- Name: data_inputs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_inputs ALTER COLUMN id SET DEFAULT nextval('public.data_inputs_id_seq'::regclass);


--
-- Name: export_access_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_access_logs ALTER COLUMN id SET DEFAULT nextval('public.export_access_logs_id_seq'::regclass);


--
-- Name: export_schedules id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_schedules ALTER COLUMN id SET DEFAULT nextval('public.export_schedules_id_seq'::regclass);


--
-- Name: export_shares id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_shares ALTER COLUMN id SET DEFAULT nextval('public.export_shares_id_seq'::regclass);


--
-- Name: export_templates id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_templates ALTER COLUMN id SET DEFAULT nextval('public.export_templates_id_seq'::regclass);


--
-- Name: file_access_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_access_logs ALTER COLUMN id SET DEFAULT nextval('public.file_access_logs_id_seq'::regclass);


--
-- Name: file_processing_jobs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_processing_jobs ALTER COLUMN id SET DEFAULT nextval('public.file_processing_jobs_id_seq'::regclass);


--
-- Name: file_records id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_records ALTER COLUMN id SET DEFAULT nextval('public.file_records_id_seq'::regclass);


--
-- Name: file_shares id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_shares ALTER COLUMN id SET DEFAULT nextval('public.file_shares_id_seq'::regclass);


--
-- Name: file_versions id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_versions ALTER COLUMN id SET DEFAULT nextval('public.file_versions_id_seq'::regclass);


--
-- Name: files id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.files ALTER COLUMN id SET DEFAULT nextval('public.files_id_seq'::regclass);


--
-- Name: records id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.records ALTER COLUMN id SET DEFAULT nextval('public.records_id_seq'::regclass);


--
-- Name: roles id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.roles ALTER COLUMN id SET DEFAULT nextval('public.roles_id_seq'::regclass);


--
-- Name: security_audit_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.security_audit_logs ALTER COLUMN id SET DEFAULT nextval('public.security_audit_logs_id_seq'::regclass);


--
-- Name: system_audit_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.system_audit_logs ALTER COLUMN id SET DEFAULT nextval('public.system_audit_logs_id_seq'::regclass);


--
-- Name: user_audit_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_audit_logs ALTER COLUMN id SET DEFAULT nextval('public.user_audit_logs_id_seq'::regclass);


--
-- Name: user_brands id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_brands ALTER COLUMN id SET DEFAULT nextval('public.user_brands_id_seq'::regclass);


--
-- Name: user_roles id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_roles ALTER COLUMN id SET DEFAULT nextval('public.user_roles_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: workflow_audit_logs id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_audit_logs ALTER COLUMN id SET DEFAULT nextval('public.workflow_audit_logs_id_seq'::regclass);


--
-- Name: workflow_requests id; Type: DEFAULT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests ALTER COLUMN id SET DEFAULT nextval('public.workflow_requests_id_seq'::regclass);


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4425.dat

--
-- Data for Name: audit_log; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4368.dat

--
-- Data for Name: brand_access_audit; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4402.dat

--
-- Data for Name: brands; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4376.dat

--
-- Data for Name: data_exports; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4406.dat

--
-- Data for Name: data_input_attribute_pending_requests; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4388.dat

--
-- Data for Name: data_input_attributes; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4382.dat

--
-- Data for Name: data_input_audit_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4418.dat

--
-- Data for Name: data_input_pending_requests; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4386.dat

--
-- Data for Name: data_inputs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4380.dat

--
-- Data for Name: export_access_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4410.dat

--
-- Data for Name: export_schedules; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4412.dat

--
-- Data for Name: export_shares; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4414.dat

--
-- Data for Name: export_templates; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4408.dat

--
-- Data for Name: file_access_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4400.dat

--
-- Data for Name: file_processing_jobs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4398.dat

--
-- Data for Name: file_records; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4392.dat

--
-- Data for Name: file_shares; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4394.dat

--
-- Data for Name: file_versions; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4396.dat

--
-- Data for Name: files; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4390.dat

--
-- Data for Name: records; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4384.dat

--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4370.dat

--
-- Data for Name: security_audit_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4422.dat

--
-- Data for Name: system_audit_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4424.dat

--
-- Data for Name: user_audit_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4416.dat

--
-- Data for Name: user_brands; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4378.dat

--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4374.dat

--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4372.dat

--
-- Data for Name: workflow_audit_logs; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4420.dat

--
-- Data for Name: workflow_requests; Type: TABLE DATA; Schema: public; Owner: ipman
--

\i $$PATH$$/4404.dat

--
-- Name: audit_log_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.audit_log_id_seq', 5266, true);


--
-- Name: brand_access_audit_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.brand_access_audit_id_seq', 1, false);


--
-- Name: brands_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.brands_id_seq', 8, true);


--
-- Name: data_exports_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.data_exports_id_seq', 1, false);


--
-- Name: data_input_attribute_pending_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.data_input_attribute_pending_requests_id_seq', 35, true);


--
-- Name: data_input_attributes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.data_input_attributes_id_seq', 119, true);


--
-- Name: data_input_audit_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.data_input_audit_logs_id_seq', 1, false);


--
-- Name: data_input_pending_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.data_input_pending_requests_id_seq', 8, true);


--
-- Name: data_inputs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.data_inputs_id_seq', 26, true);


--
-- Name: export_access_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.export_access_logs_id_seq', 1, false);


--
-- Name: export_schedules_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.export_schedules_id_seq', 1, false);


--
-- Name: export_shares_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.export_shares_id_seq', 1, false);


--
-- Name: export_templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.export_templates_id_seq', 6, true);


--
-- Name: file_access_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.file_access_logs_id_seq', 1, false);


--
-- Name: file_processing_jobs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.file_processing_jobs_id_seq', 1, false);


--
-- Name: file_records_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.file_records_id_seq', 1, false);


--
-- Name: file_shares_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.file_shares_id_seq', 1, false);


--
-- Name: file_versions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.file_versions_id_seq', 1, false);


--
-- Name: files_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.files_id_seq', 1, false);


--
-- Name: records_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.records_id_seq', 4829, true);


--
-- Name: roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.roles_id_seq', 9, true);


--
-- Name: security_audit_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.security_audit_logs_id_seq', 2, true);


--
-- Name: system_audit_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.system_audit_logs_id_seq', 1, false);


--
-- Name: user_audit_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.user_audit_logs_id_seq', 2, true);


--
-- Name: user_brands_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.user_brands_id_seq', 5, true);


--
-- Name: user_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.user_roles_id_seq', 8, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.users_id_seq', 13, true);


--
-- Name: workflow_audit_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.workflow_audit_logs_id_seq', 1, false);


--
-- Name: workflow_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: ipman
--

SELECT pg_catalog.setval('public.workflow_requests_id_seq', 1, false);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: audit_log audit_log_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.audit_log
    ADD CONSTRAINT audit_log_pkey PRIMARY KEY (id);


--
-- Name: brand_access_audit brand_access_audit_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brand_access_audit
    ADD CONSTRAINT brand_access_audit_pkey PRIMARY KEY (id);


--
-- Name: brands brands_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brands
    ADD CONSTRAINT brands_name_key UNIQUE (name);


--
-- Name: brands brands_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brands
    ADD CONSTRAINT brands_pkey PRIMARY KEY (id);


--
-- Name: data_exports data_exports_export_id_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_exports
    ADD CONSTRAINT data_exports_export_id_key UNIQUE (export_id);


--
-- Name: data_exports data_exports_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_exports
    ADD CONSTRAINT data_exports_pkey PRIMARY KEY (id);


--
-- Name: data_input_attribute_pending_requests data_input_attribute_pending_requests_data_input_id_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests
    ADD CONSTRAINT data_input_attribute_pending_requests_data_input_id_name_key UNIQUE (data_input_id, name);


--
-- Name: data_input_attribute_pending_requests data_input_attribute_pending_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests
    ADD CONSTRAINT data_input_attribute_pending_requests_pkey PRIMARY KEY (id);


--
-- Name: data_input_attributes data_input_attributes_data_input_id_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attributes
    ADD CONSTRAINT data_input_attributes_data_input_id_name_key UNIQUE (data_input_id, name);


--
-- Name: data_input_attributes data_input_attributes_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attributes
    ADD CONSTRAINT data_input_attributes_pkey PRIMARY KEY (id);


--
-- Name: data_input_audit_logs data_input_audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_audit_logs
    ADD CONSTRAINT data_input_audit_logs_pkey PRIMARY KEY (id);


--
-- Name: data_input_pending_requests data_input_pending_requests_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_pending_requests
    ADD CONSTRAINT data_input_pending_requests_name_key UNIQUE (name);


--
-- Name: data_input_pending_requests data_input_pending_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_pending_requests
    ADD CONSTRAINT data_input_pending_requests_pkey PRIMARY KEY (id);


--
-- Name: data_inputs data_inputs_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_inputs
    ADD CONSTRAINT data_inputs_name_key UNIQUE (name);


--
-- Name: data_inputs data_inputs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_inputs
    ADD CONSTRAINT data_inputs_pkey PRIMARY KEY (id);


--
-- Name: export_access_logs export_access_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_access_logs
    ADD CONSTRAINT export_access_logs_pkey PRIMARY KEY (id);


--
-- Name: export_schedules export_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_schedules
    ADD CONSTRAINT export_schedules_pkey PRIMARY KEY (id);


--
-- Name: export_shares export_shares_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_shares
    ADD CONSTRAINT export_shares_pkey PRIMARY KEY (id);


--
-- Name: export_shares export_shares_share_token_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_shares
    ADD CONSTRAINT export_shares_share_token_key UNIQUE (share_token);


--
-- Name: export_templates export_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_templates
    ADD CONSTRAINT export_templates_pkey PRIMARY KEY (id);


--
-- Name: file_access_logs file_access_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_access_logs
    ADD CONSTRAINT file_access_logs_pkey PRIMARY KEY (id);


--
-- Name: file_processing_jobs file_processing_jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_processing_jobs
    ADD CONSTRAINT file_processing_jobs_pkey PRIMARY KEY (id);


--
-- Name: file_records file_records_file_id_record_id_attribute_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_records
    ADD CONSTRAINT file_records_file_id_record_id_attribute_name_key UNIQUE (file_id, record_id, attribute_name);


--
-- Name: file_records file_records_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_records
    ADD CONSTRAINT file_records_pkey PRIMARY KEY (id);


--
-- Name: file_shares file_shares_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_shares
    ADD CONSTRAINT file_shares_pkey PRIMARY KEY (id);


--
-- Name: file_shares file_shares_share_token_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_shares
    ADD CONSTRAINT file_shares_share_token_key UNIQUE (share_token);


--
-- Name: file_versions file_versions_file_id_version_number_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_versions
    ADD CONSTRAINT file_versions_file_id_version_number_key UNIQUE (file_id, version_number);


--
-- Name: file_versions file_versions_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_versions
    ADD CONSTRAINT file_versions_pkey PRIMARY KEY (id);


--
-- Name: files files_filename_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_filename_key UNIQUE (filename);


--
-- Name: files files_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_pkey PRIMARY KEY (id);


--
-- Name: records records_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.records
    ADD CONSTRAINT records_pkey PRIMARY KEY (id);


--
-- Name: roles roles_name_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_name_key UNIQUE (name);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: security_audit_logs security_audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.security_audit_logs
    ADD CONSTRAINT security_audit_logs_pkey PRIMARY KEY (id);


--
-- Name: system_audit_logs system_audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.system_audit_logs
    ADD CONSTRAINT system_audit_logs_pkey PRIMARY KEY (id);


--
-- Name: user_audit_logs user_audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_audit_logs
    ADD CONSTRAINT user_audit_logs_pkey PRIMARY KEY (id);


--
-- Name: user_brands user_brands_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_brands
    ADD CONSTRAINT user_brands_pkey PRIMARY KEY (id);


--
-- Name: user_brands user_brands_user_id_brand_id_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_brands
    ADD CONSTRAINT user_brands_user_id_brand_id_key UNIQUE (user_id, brand_id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_user_id_role_id_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_role_id_key UNIQUE (user_id, role_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: workflow_audit_logs workflow_audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_audit_logs
    ADD CONSTRAINT workflow_audit_logs_pkey PRIMARY KEY (id);


--
-- Name: workflow_requests workflow_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT workflow_requests_pkey PRIMARY KEY (id);


--
-- Name: idx_brand_access_audit_action; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_brand_access_audit_action ON public.brand_access_audit USING btree (action);


--
-- Name: idx_brand_access_audit_brand; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_brand_access_audit_brand ON public.brand_access_audit USING btree (brand_id);


--
-- Name: idx_brand_access_audit_performed_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_brand_access_audit_performed_at ON public.brand_access_audit USING btree (performed_at);


--
-- Name: idx_brand_access_audit_user; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_brand_access_audit_user ON public.brand_access_audit USING btree (user_id);


--
-- Name: idx_data_exports_brand_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_brand_id ON public.data_exports USING btree (brand_id);


--
-- Name: idx_data_exports_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_created_at ON public.data_exports USING btree (created_at);


--
-- Name: idx_data_exports_data_input_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_data_input_id ON public.data_exports USING btree (data_input_id);


--
-- Name: idx_data_exports_expires_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_expires_at ON public.data_exports USING btree (expires_at);


--
-- Name: idx_data_exports_export_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_export_id ON public.data_exports USING btree (export_id);


--
-- Name: idx_data_exports_requested_by; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_requested_by ON public.data_exports USING btree (requested_by);


--
-- Name: idx_data_exports_status; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_exports_status ON public.data_exports USING btree (status);


--
-- Name: idx_data_input_attributes_data_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_attributes_data_type ON public.data_input_attributes USING btree (data_type);


--
-- Name: idx_data_input_attributes_display_order; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_attributes_display_order ON public.data_input_attributes USING btree (display_order);


--
-- Name: idx_data_input_attributes_required; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_attributes_required ON public.data_input_attributes USING btree (is_required);


--
-- Name: idx_data_input_attributes_unique; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_attributes_unique ON public.data_input_attributes USING btree (is_unique);


--
-- Name: idx_data_input_audit_logs_action; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_action ON public.data_input_audit_logs USING btree (action);


--
-- Name: idx_data_input_audit_logs_brand_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_brand_id ON public.data_input_audit_logs USING btree (brand_id);


--
-- Name: idx_data_input_audit_logs_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_created_at ON public.data_input_audit_logs USING btree (created_at);


--
-- Name: idx_data_input_audit_logs_data_input_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_data_input_id ON public.data_input_audit_logs USING btree (data_input_id);


--
-- Name: idx_data_input_audit_logs_resource_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_resource_type ON public.data_input_audit_logs USING btree (resource_type);


--
-- Name: idx_data_input_audit_logs_severity; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_severity ON public.data_input_audit_logs USING btree (severity);


--
-- Name: idx_data_input_audit_logs_success; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_success ON public.data_input_audit_logs USING btree (success);


--
-- Name: idx_data_input_audit_logs_user_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_input_audit_logs_user_id ON public.data_input_audit_logs USING btree (user_id);


--
-- Name: idx_data_inputs_is_deleted; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_data_inputs_is_deleted ON public.data_inputs USING btree (is_deleted);


--
-- Name: idx_export_access_logs_access_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_access_logs_access_type ON public.export_access_logs USING btree (access_type);


--
-- Name: idx_export_access_logs_accessed_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_access_logs_accessed_at ON public.export_access_logs USING btree (accessed_at);


--
-- Name: idx_export_access_logs_export_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_access_logs_export_id ON public.export_access_logs USING btree (export_id);


--
-- Name: idx_export_schedules_data_input_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_schedules_data_input_id ON public.export_schedules USING btree (data_input_id);


--
-- Name: idx_export_schedules_is_active; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_schedules_is_active ON public.export_schedules USING btree (is_active);


--
-- Name: idx_export_schedules_next_run_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_schedules_next_run_at ON public.export_schedules USING btree (next_run_at);


--
-- Name: idx_export_shares_expires_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_shares_expires_at ON public.export_shares USING btree (expires_at);


--
-- Name: idx_export_shares_export_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_shares_export_id ON public.export_shares USING btree (export_id);


--
-- Name: idx_export_shares_share_token; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_shares_share_token ON public.export_shares USING btree (share_token);


--
-- Name: idx_export_templates_data_input_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_templates_data_input_id ON public.export_templates USING btree (data_input_id);


--
-- Name: idx_export_templates_is_active; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_templates_is_active ON public.export_templates USING btree (is_active);


--
-- Name: idx_export_templates_is_public; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_export_templates_is_public ON public.export_templates USING btree (is_public);


--
-- Name: idx_file_access_logs_access_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_access_logs_access_type ON public.file_access_logs USING btree (access_type);


--
-- Name: idx_file_access_logs_accessed_by; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_access_logs_accessed_by ON public.file_access_logs USING btree (accessed_by);


--
-- Name: idx_file_access_logs_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_access_logs_created_at ON public.file_access_logs USING btree (created_at);


--
-- Name: idx_file_access_logs_file_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_access_logs_file_id ON public.file_access_logs USING btree (file_id);


--
-- Name: idx_file_processing_jobs_file_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_processing_jobs_file_id ON public.file_processing_jobs USING btree (file_id);


--
-- Name: idx_file_processing_jobs_priority; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_processing_jobs_priority ON public.file_processing_jobs USING btree (priority);


--
-- Name: idx_file_processing_jobs_status; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_processing_jobs_status ON public.file_processing_jobs USING btree (status);


--
-- Name: idx_file_records_attribute_name; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_records_attribute_name ON public.file_records USING btree (attribute_name);


--
-- Name: idx_file_records_file_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_records_file_id ON public.file_records USING btree (file_id);


--
-- Name: idx_file_records_record_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_records_record_id ON public.file_records USING btree (record_id);


--
-- Name: idx_file_shares_expires_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_shares_expires_at ON public.file_shares USING btree (expires_at);


--
-- Name: idx_file_shares_file_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_shares_file_id ON public.file_shares USING btree (file_id);


--
-- Name: idx_file_shares_is_active; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_shares_is_active ON public.file_shares USING btree (is_active);


--
-- Name: idx_file_shares_share_token; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_shares_share_token ON public.file_shares USING btree (share_token);


--
-- Name: idx_file_versions_file_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_versions_file_id ON public.file_versions USING btree (file_id);


--
-- Name: idx_file_versions_version_number; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_file_versions_version_number ON public.file_versions USING btree (version_number);


--
-- Name: idx_files_brand_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_brand_id ON public.files USING btree (brand_id);


--
-- Name: idx_files_checksum_md5; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_checksum_md5 ON public.files USING btree (checksum_md5);


--
-- Name: idx_files_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_created_at ON public.files USING btree (created_at);


--
-- Name: idx_files_file_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_file_type ON public.files USING btree (file_type);


--
-- Name: idx_files_filename; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_filename ON public.files USING btree (filename);


--
-- Name: idx_files_is_deleted; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_is_deleted ON public.files USING btree (is_deleted);


--
-- Name: idx_files_is_public; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_is_public ON public.files USING btree (is_public);


--
-- Name: idx_files_status; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_status ON public.files USING btree (status);


--
-- Name: idx_files_uploaded_by; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_files_uploaded_by ON public.files USING btree (uploaded_by);


--
-- Name: idx_records_is_deleted; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_records_is_deleted ON public.records USING btree (is_deleted);


--
-- Name: idx_security_audit_logs_action; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_action ON public.security_audit_logs USING btree (action);


--
-- Name: idx_security_audit_logs_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_created_at ON public.security_audit_logs USING btree (created_at);


--
-- Name: idx_security_audit_logs_event_category; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_event_category ON public.security_audit_logs USING btree (event_category);


--
-- Name: idx_security_audit_logs_event_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_event_type ON public.security_audit_logs USING btree (event_type);


--
-- Name: idx_security_audit_logs_ip_address; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_ip_address ON public.security_audit_logs USING btree (ip_address);


--
-- Name: idx_security_audit_logs_severity; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_severity ON public.security_audit_logs USING btree (severity);


--
-- Name: idx_security_audit_logs_success; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_success ON public.security_audit_logs USING btree (success);


--
-- Name: idx_security_audit_logs_user_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_user_id ON public.security_audit_logs USING btree (user_id);


--
-- Name: idx_security_audit_logs_username_attempted; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_security_audit_logs_username_attempted ON public.security_audit_logs USING btree (username_attempted);


--
-- Name: idx_system_audit_logs_action; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_action ON public.system_audit_logs USING btree (action);


--
-- Name: idx_system_audit_logs_component; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_component ON public.system_audit_logs USING btree (component);


--
-- Name: idx_system_audit_logs_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_created_at ON public.system_audit_logs USING btree (created_at);


--
-- Name: idx_system_audit_logs_operation_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_operation_type ON public.system_audit_logs USING btree (operation_type);


--
-- Name: idx_system_audit_logs_severity; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_severity ON public.system_audit_logs USING btree (severity);


--
-- Name: idx_system_audit_logs_success; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_success ON public.system_audit_logs USING btree (success);


--
-- Name: idx_system_audit_logs_user_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_system_audit_logs_user_id ON public.system_audit_logs USING btree (user_id);


--
-- Name: idx_user_audit_logs_action; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_action ON public.user_audit_logs USING btree (action);


--
-- Name: idx_user_audit_logs_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_created_at ON public.user_audit_logs USING btree (created_at);


--
-- Name: idx_user_audit_logs_resource_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_resource_id ON public.user_audit_logs USING btree (resource_id);


--
-- Name: idx_user_audit_logs_resource_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_resource_type ON public.user_audit_logs USING btree (resource_type);


--
-- Name: idx_user_audit_logs_severity; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_severity ON public.user_audit_logs USING btree (severity);


--
-- Name: idx_user_audit_logs_success; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_success ON public.user_audit_logs USING btree (success);


--
-- Name: idx_user_audit_logs_target_user_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_target_user_id ON public.user_audit_logs USING btree (target_user_id);


--
-- Name: idx_user_audit_logs_user_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_audit_logs_user_id ON public.user_audit_logs USING btree (user_id);


--
-- Name: idx_user_brands_active; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_brands_active ON public.user_brands USING btree (is_active);


--
-- Name: idx_user_brands_permissions; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_brands_permissions ON public.user_brands USING btree (can_view, can_create, can_edit, can_delete);


--
-- Name: idx_user_brands_role; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_user_brands_role ON public.user_brands USING btree (brand_role);


--
-- Name: idx_workflow_audit_logs_action; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_action ON public.workflow_audit_logs USING btree (action);


--
-- Name: idx_workflow_audit_logs_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_created_at ON public.workflow_audit_logs USING btree (created_at);


--
-- Name: idx_workflow_audit_logs_new_status; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_new_status ON public.workflow_audit_logs USING btree (new_status);


--
-- Name: idx_workflow_audit_logs_old_status; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_old_status ON public.workflow_audit_logs USING btree (old_status);


--
-- Name: idx_workflow_audit_logs_request_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_request_id ON public.workflow_audit_logs USING btree (request_id);


--
-- Name: idx_workflow_audit_logs_severity; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_severity ON public.workflow_audit_logs USING btree (severity);


--
-- Name: idx_workflow_audit_logs_success; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_success ON public.workflow_audit_logs USING btree (success);


--
-- Name: idx_workflow_audit_logs_user_id; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_audit_logs_user_id ON public.workflow_audit_logs USING btree (user_id);


--
-- Name: idx_workflow_requests_approver; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_approver ON public.workflow_requests USING btree (approver_id);


--
-- Name: idx_workflow_requests_brand; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_brand ON public.workflow_requests USING btree (brand_id);


--
-- Name: idx_workflow_requests_created_at; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_created_at ON public.workflow_requests USING btree (created_at);


--
-- Name: idx_workflow_requests_data_input; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_data_input ON public.workflow_requests USING btree (data_input_id);


--
-- Name: idx_workflow_requests_priority; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_priority ON public.workflow_requests USING btree (priority);


--
-- Name: idx_workflow_requests_requester; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_requester ON public.workflow_requests USING btree (requester_id);


--
-- Name: idx_workflow_requests_reviewer; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_reviewer ON public.workflow_requests USING btree (reviewer_id);


--
-- Name: idx_workflow_requests_status; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_status ON public.workflow_requests USING btree (status);


--
-- Name: idx_workflow_requests_type; Type: INDEX; Schema: public; Owner: ipman
--

CREATE INDEX idx_workflow_requests_type ON public.workflow_requests USING btree (request_type);


--
-- Name: file_summary _RETURN; Type: RULE; Schema: public; Owner: ipman
--

CREATE OR REPLACE VIEW public.file_summary AS
 SELECT f.id,
    f.original_filename,
    f.filename,
    f.file_size,
    f.mime_type,
    f.file_type,
    f.status,
    f.uploaded_by,
    f.brand_id,
    b.name AS brand_name,
    f.is_public,
    f.download_count,
    f.has_thumbnail,
    f.created_at,
    f.updated_at,
    count(fr.id) AS record_associations,
    count(fs.id) AS active_shares
   FROM (((public.files f
     LEFT JOIN public.brands b ON ((f.brand_id = b.id)))
     LEFT JOIN public.file_records fr ON ((f.id = fr.file_id)))
     LEFT JOIN public.file_shares fs ON (((f.id = fs.file_id) AND (fs.is_active = true))))
  WHERE (f.is_deleted = false)
  GROUP BY f.id, b.name;


--
-- Name: brands audit_brands_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_brands_trigger AFTER INSERT OR DELETE OR UPDATE ON public.brands FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: data_input_attribute_pending_requests audit_data_input_attribute_pending_requests_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_data_input_attribute_pending_requests_trigger AFTER INSERT OR DELETE OR UPDATE ON public.data_input_attribute_pending_requests FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: data_input_attributes audit_data_input_attributes_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_data_input_attributes_trigger AFTER INSERT OR DELETE OR UPDATE ON public.data_input_attributes FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: data_input_pending_requests audit_data_input_pending_requests_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_data_input_pending_requests_trigger AFTER INSERT OR DELETE OR UPDATE ON public.data_input_pending_requests FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: data_inputs audit_data_inputs_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_data_inputs_trigger AFTER INSERT OR DELETE OR UPDATE ON public.data_inputs FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: records audit_records_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_records_trigger AFTER INSERT OR DELETE OR UPDATE ON public.records FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: roles audit_roles_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_roles_trigger AFTER INSERT OR DELETE OR UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: user_brands audit_user_brands_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_user_brands_trigger AFTER INSERT OR DELETE OR UPDATE ON public.user_brands FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: user_roles audit_user_roles_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_user_roles_trigger AFTER INSERT OR DELETE OR UPDATE ON public.user_roles FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: users audit_users_trigger; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER audit_users_trigger AFTER INSERT OR DELETE OR UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.log_changes();


--
-- Name: user_brands trigger_log_brand_access_changes; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_log_brand_access_changes AFTER INSERT OR UPDATE ON public.user_brands FOR EACH ROW EXECUTE FUNCTION public.log_brand_access_changes();


--
-- Name: brands trigger_update_brands_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_brands_updated_at BEFORE UPDATE ON public.brands FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: data_input_attribute_pending_requests trigger_update_data_input_attribute_pending_requests_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_data_input_attribute_pending_requests_updated_at BEFORE UPDATE ON public.data_input_attribute_pending_requests FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: data_input_attributes trigger_update_data_input_attributes_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_data_input_attributes_updated_at BEFORE UPDATE ON public.data_input_attributes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: data_input_pending_requests trigger_update_data_input_pending_requests_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_data_input_pending_requests_updated_at BEFORE UPDATE ON public.data_input_pending_requests FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: data_inputs trigger_update_data_inputs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_data_inputs_updated_at BEFORE UPDATE ON public.data_inputs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_brands trigger_update_permissions_on_role_change; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_permissions_on_role_change BEFORE UPDATE ON public.user_brands FOR EACH ROW EXECUTE FUNCTION public.update_permissions_on_role_change();


--
-- Name: records trigger_update_records_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_records_updated_at BEFORE UPDATE ON public.records FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: roles trigger_update_roles_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_roles_updated_at BEFORE UPDATE ON public.roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_brands trigger_update_user_brands_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_user_brands_updated_at BEFORE UPDATE ON public.user_brands FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_roles trigger_update_user_roles_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_user_roles_updated_at BEFORE UPDATE ON public.user_roles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users trigger_update_users_timestamps; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER trigger_update_users_timestamps BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_user_timestamps();


--
-- Name: data_exports update_data_exports_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_data_exports_updated_at BEFORE UPDATE ON public.data_exports FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: data_input_audit_logs update_data_input_audit_logs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_data_input_audit_logs_updated_at BEFORE UPDATE ON public.data_input_audit_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: export_schedules update_export_schedules_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_export_schedules_updated_at BEFORE UPDATE ON public.export_schedules FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: export_templates update_export_templates_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_export_templates_updated_at BEFORE UPDATE ON public.export_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: file_processing_jobs update_file_processing_jobs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_file_processing_jobs_updated_at BEFORE UPDATE ON public.file_processing_jobs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: file_records update_file_records_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_file_records_updated_at BEFORE UPDATE ON public.file_records FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: file_shares update_file_shares_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_file_shares_updated_at BEFORE UPDATE ON public.file_shares FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: file_versions update_file_versions_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_file_versions_updated_at BEFORE UPDATE ON public.file_versions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: files update_files_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_files_updated_at BEFORE UPDATE ON public.files FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: security_audit_logs update_security_audit_logs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_security_audit_logs_updated_at BEFORE UPDATE ON public.security_audit_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: system_audit_logs update_system_audit_logs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_system_audit_logs_updated_at BEFORE UPDATE ON public.system_audit_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_audit_logs update_user_audit_logs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_user_audit_logs_updated_at BEFORE UPDATE ON public.user_audit_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: workflow_audit_logs update_workflow_audit_logs_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_workflow_audit_logs_updated_at BEFORE UPDATE ON public.workflow_audit_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: workflow_requests update_workflow_requests_updated_at; Type: TRIGGER; Schema: public; Owner: ipman
--

CREATE TRIGGER update_workflow_requests_updated_at BEFORE UPDATE ON public.workflow_requests FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: brand_access_audit brand_access_audit_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brand_access_audit
    ADD CONSTRAINT brand_access_audit_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id);


--
-- Name: brand_access_audit brand_access_audit_performed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brand_access_audit
    ADD CONSTRAINT brand_access_audit_performed_by_fkey FOREIGN KEY (performed_by) REFERENCES public.users(id);


--
-- Name: brand_access_audit brand_access_audit_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.brand_access_audit
    ADD CONSTRAINT brand_access_audit_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: data_exports data_exports_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_exports
    ADD CONSTRAINT data_exports_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE SET NULL;


--
-- Name: data_exports data_exports_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_exports
    ADD CONSTRAINT data_exports_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE CASCADE;


--
-- Name: data_exports data_exports_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_exports
    ADD CONSTRAINT data_exports_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE SET NULL;


--
-- Name: data_input_attribute_pending_requests data_input_attribute_pending__data_input_pending_request_i_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests
    ADD CONSTRAINT data_input_attribute_pending__data_input_pending_request_i_fkey FOREIGN KEY (data_input_pending_request_id) REFERENCES public.data_input_pending_requests(id) ON DELETE CASCADE;


--
-- Name: data_input_attribute_pending_requests data_input_attribute_pending__relationship_target_data_inp_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests
    ADD CONSTRAINT data_input_attribute_pending__relationship_target_data_inp_fkey FOREIGN KEY (relationship_target_data_input_id) REFERENCES public.data_inputs(id) ON DELETE SET NULL;


--
-- Name: data_input_attribute_pending_requests data_input_attribute_pending_requests_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests
    ADD CONSTRAINT data_input_attribute_pending_requests_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE CASCADE;


--
-- Name: data_input_attribute_pending_requests data_input_attribute_pending_requests_workflow_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attribute_pending_requests
    ADD CONSTRAINT data_input_attribute_pending_requests_workflow_request_id_fkey FOREIGN KEY (workflow_request_id) REFERENCES public.workflow_requests(id);


--
-- Name: data_input_attributes data_input_attributes_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attributes
    ADD CONSTRAINT data_input_attributes_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE CASCADE;


--
-- Name: data_input_attributes data_input_attributes_relationship_target_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_attributes
    ADD CONSTRAINT data_input_attributes_relationship_target_data_input_id_fkey FOREIGN KEY (relationship_target_data_input_id) REFERENCES public.data_inputs(id) ON DELETE SET NULL;


--
-- Name: data_input_audit_logs data_input_audit_logs_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_audit_logs
    ADD CONSTRAINT data_input_audit_logs_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE SET NULL;


--
-- Name: data_input_audit_logs data_input_audit_logs_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_audit_logs
    ADD CONSTRAINT data_input_audit_logs_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE SET NULL;


--
-- Name: data_input_audit_logs data_input_audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_audit_logs
    ADD CONSTRAINT data_input_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: data_input_pending_requests data_input_pending_requests_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_pending_requests
    ADD CONSTRAINT data_input_pending_requests_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE CASCADE;


--
-- Name: data_input_pending_requests data_input_pending_requests_workflow_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_input_pending_requests
    ADD CONSTRAINT data_input_pending_requests_workflow_request_id_fkey FOREIGN KEY (workflow_request_id) REFERENCES public.workflow_requests(id);


--
-- Name: data_inputs data_inputs_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.data_inputs
    ADD CONSTRAINT data_inputs_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE CASCADE;


--
-- Name: export_access_logs export_access_logs_export_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_access_logs
    ADD CONSTRAINT export_access_logs_export_id_fkey FOREIGN KEY (export_id) REFERENCES public.data_exports(id) ON DELETE CASCADE;


--
-- Name: export_schedules export_schedules_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_schedules
    ADD CONSTRAINT export_schedules_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE CASCADE;


--
-- Name: export_shares export_shares_export_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_shares
    ADD CONSTRAINT export_shares_export_id_fkey FOREIGN KEY (export_id) REFERENCES public.data_exports(id) ON DELETE CASCADE;


--
-- Name: export_templates export_templates_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.export_templates
    ADD CONSTRAINT export_templates_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE CASCADE;


--
-- Name: file_access_logs file_access_logs_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_access_logs
    ADD CONSTRAINT file_access_logs_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: file_processing_jobs file_processing_jobs_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_processing_jobs
    ADD CONSTRAINT file_processing_jobs_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: file_records file_records_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_records
    ADD CONSTRAINT file_records_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: file_records file_records_record_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_records
    ADD CONSTRAINT file_records_record_id_fkey FOREIGN KEY (record_id) REFERENCES public.records(id) ON DELETE CASCADE;


--
-- Name: file_shares file_shares_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_shares
    ADD CONSTRAINT file_shares_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: file_versions file_versions_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.file_versions
    ADD CONSTRAINT file_versions_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON DELETE CASCADE;


--
-- Name: files files_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id);


--
-- Name: workflow_requests fk_workflow_requests_approver; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT fk_workflow_requests_approver FOREIGN KEY (approver_id) REFERENCES public.users(id);


--
-- Name: workflow_requests fk_workflow_requests_brand; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT fk_workflow_requests_brand FOREIGN KEY (brand_id) REFERENCES public.brands(id);


--
-- Name: workflow_requests fk_workflow_requests_data_input; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT fk_workflow_requests_data_input FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id);


--
-- Name: workflow_requests fk_workflow_requests_requester; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT fk_workflow_requests_requester FOREIGN KEY (requester_id) REFERENCES public.users(id);


--
-- Name: workflow_requests fk_workflow_requests_reviewer; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT fk_workflow_requests_reviewer FOREIGN KEY (reviewer_id) REFERENCES public.users(id);


--
-- Name: records records_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.records
    ADD CONSTRAINT records_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id) ON DELETE CASCADE;


--
-- Name: security_audit_logs security_audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.security_audit_logs
    ADD CONSTRAINT security_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: system_audit_logs system_audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.system_audit_logs
    ADD CONSTRAINT system_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_audit_logs user_audit_logs_target_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_audit_logs
    ADD CONSTRAINT user_audit_logs_target_user_id_fkey FOREIGN KEY (target_user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_audit_logs user_audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_audit_logs
    ADD CONSTRAINT user_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_brands user_brands_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_brands
    ADD CONSTRAINT user_brands_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE CASCADE;


--
-- Name: user_brands user_brands_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_brands
    ADD CONSTRAINT user_brands_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users users_changed_by_fk_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_changed_by_fk_fkey FOREIGN KEY (changed_by_fk) REFERENCES public.users(id);


--
-- Name: users users_created_by_fk_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_created_by_fk_fkey FOREIGN KEY (created_by_fk) REFERENCES public.users(id);


--
-- Name: workflow_audit_logs workflow_audit_logs_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_audit_logs
    ADD CONSTRAINT workflow_audit_logs_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id) ON DELETE SET NULL;


--
-- Name: workflow_audit_logs workflow_audit_logs_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_audit_logs
    ADD CONSTRAINT workflow_audit_logs_request_id_fkey FOREIGN KEY (request_id) REFERENCES public.workflow_requests(id) ON DELETE SET NULL;


--
-- Name: workflow_audit_logs workflow_audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_audit_logs
    ADD CONSTRAINT workflow_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: workflow_requests workflow_requests_approver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT workflow_requests_approver_id_fkey FOREIGN KEY (approver_id) REFERENCES public.users(id);


--
-- Name: workflow_requests workflow_requests_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT workflow_requests_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES public.brands(id);


--
-- Name: workflow_requests workflow_requests_data_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT workflow_requests_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES public.data_inputs(id);


--
-- Name: workflow_requests workflow_requests_requester_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT workflow_requests_requester_id_fkey FOREIGN KEY (requester_id) REFERENCES public.users(id);


--
-- Name: workflow_requests workflow_requests_reviewer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ipman
--

ALTER TABLE ONLY public.workflow_requests
    ADD CONSTRAINT workflow_requests_reviewer_id_fkey FOREIGN KEY (reviewer_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

