INSERT INTO public.data_input_attribute_pending_requests VALUES (1, NULL, 'competitor_name', 'Name of the competing clinic or brand.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.274617+07', 'system_setup', 1, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (2, NULL, 'treatment_name', 'The specific treatment being compared.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.29348+07', 'system_setup', 1, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (3, NULL, 'listed_price_idr', 'The competitor''s listed price in IDR.', 'PENDING', 'Initial proposal', 'Decimal', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.294209+07', 'system_setup', 1, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (4, NULL, 'monitoring_date', 'Date the price was checked.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.294954+07', 'system_setup', 1, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (5, NULL, 'practitioner_name', 'Full name of the practitioner.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.295514+07', 'system_setup', 2, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (6, NULL, 'certification_name', 'Name of the license or certification.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.296121+07', 'system_setup', 2, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (7, NULL, 'issuing_body', 'The organization that issued the certificate.', 'PENDING', 'Initial proposal', 'Text', false, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.296607+07', 'system_setup', 2, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (8, NULL, 'expiry_date', 'The date the certification expires.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.29704+07', 'system_setup', 2, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (9, NULL, 'referring_patient_phone', 'Phone number of the patient who made the referral.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.297427+07', 'system_setup', 3, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (10, NULL, 'new_patient_phone', 'Phone number of the new patient who was referred.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.297871+07', 'system_setup', 3, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (11, NULL, 'referral_date', 'Date the new patient registered.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.298403+07', 'system_setup', 3, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (12, NULL, 'bonus_status', 'Status of the bonus for the referrer (e.g., Pending, Awarded).', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.309876+07', 'system_setup', 3, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (13, NULL, 'patient_name', 'Name of the patient for the work order.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.310607+07', 'system_setup', 4, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (14, NULL, 'lab_name', 'Name of the external dental lab.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.311517+07', 'system_setup', 4, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (15, NULL, 'item_description', 'Description of the work (e.g., Zirconia Crown, Molar 36).', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.312074+07', 'system_setup', 4, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (16, NULL, 'due_date', 'Date the completed work is needed back from the lab.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.312647+07', 'system_setup', 4, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (17, NULL, 'partner_name', 'Legal name of the partner company or individual.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.313176+07', 'system_setup', 5, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (18, NULL, 'agreement_type', 'Type of agreement (e.g., Referral, Co-branding).', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.313654+07', 'system_setup', 5, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (19, NULL, 'agreement_document', 'Link to the signed contract in GCS.', 'PENDING', 'Initial proposal', 'URL', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.314319+07', 'system_setup', 5, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (20, NULL, 'expiry_date', 'Contract expiration date.', 'PENDING', 'Initial proposal', 'Date', false, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.315077+07', 'system_setup', 5, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (21, NULL, 'module_title', 'Title of the e-learning module.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.315736+07', 'system_setup', 6, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (22, NULL, 'content_lead', 'Name of the person leading development.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.316709+07', 'system_setup', 6, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (23, NULL, 'status', 'Current stage (e.g., Scripting, Shooting, Editing, Published).', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.317243+07', 'system_setup', 6, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (24, NULL, 'target_completion_date', 'The target date for publishing the module.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.317743+07', 'system_setup', 6, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (25, NULL, 'platform_name', 'Name of the delivery platform (e.g., GoFood, GrabFood).', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.318188+07', 'system_setup', 7, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (26, NULL, 'report_date', 'The date the data is for.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.318572+07', 'system_setup', 7, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (27, NULL, 'gross_sales_idr', 'Total sales value from the platform''s dashboard.', 'PENDING', 'Initial proposal', 'Decimal', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.318997+07', 'system_setup', 7, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (28, NULL, 'commission_amount_idr', 'Total commission paid to the platform.', 'PENDING', 'Initial proposal', 'Decimal', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.319389+07', 'system_setup', 7, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (29, NULL, 'reported_issues', 'Any issues encountered (e.g., driver problems, app downtime).', 'PENDING', 'Initial proposal', 'Text', false, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.319833+07', 'system_setup', 7, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (30, NULL, 'supplier_name', 'Name of the supplier being audited.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.320186+07', 'system_setup', 8, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (31, NULL, 'audit_date', 'Date of the audit.', 'PENDING', 'Initial proposal', 'Date', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.320728+07', 'system_setup', 8, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (32, NULL, 'auditor_name', 'Name of the person from Golden Lamian conducting the audit.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.321261+07', 'system_setup', 8, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (33, NULL, 'overall_score', 'The final score of the audit (e.g., 1-100).', 'PENDING', 'Initial proposal', 'Number', true, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.321663+07', 'system_setup', 8, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (34, NULL, 'corrective_actions', 'List of required corrective actions for the supplier.', 'PENDING', 'Initial proposal', 'Text', false, false, true, 1, '2025-06-12 20:35:31.289839+07', 'system_setup', '2025-06-12 20:38:57.322033+07', 'system_setup', 8, NULL, NULL, NULL);
INSERT INTO public.data_input_attribute_pending_requests VALUES (35, NULL, 'patient_record', 'A reference to the official patient record.', 'PENDING', 'Initial proposal', 'Text', true, false, true, 1, '2025-06-12 20:40:04.417181+07', 'system_setup', '2025-06-12 20:40:36.881861+07', 'system_setup', 4, 17, 'full_name', NULL);


