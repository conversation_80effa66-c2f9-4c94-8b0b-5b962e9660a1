INSERT INTO public.data_inputs VALUES (5, 'Patient Appointment Schedule', 2, 'A log of all patient appointments, including date, time, and purpose.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (6, 'Sterilization Equipment Maintenance Log', 2, 'A compliance log for routine maintenance of sterilization equipment.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (8, 'Dentist & Staff Weekly Timesheet', 2, 'Tracks weekly hours for payroll purposes for all clinic staff.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (9, 'Corporate Client Inquiry Form', 3, 'Captures initial inquiries from potential corporate clients.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (10, 'Training Venue & Resource Booking', 3, 'Form to book venues, projectors, and other resources for a training event.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (11, 'New Course Proposal & Syllabus', 3, 'A structured form for submitting proposals for new training programs.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (12, 'Instructor Expense Claim Form', 3, 'Allows instructors to claim expenses for travel, materials, etc.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (13, 'Outlet Cleanliness & Hygiene Checklist', 4, 'A daily checklist for managers to ensure compliance with health standards.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (14, 'Customer Complaint & Resolution Log', 4, 'Tracks customer complaints and the steps taken to resolve them.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (15, 'New Employee Onboarding Checklist', 4, 'An HR form to track the onboarding process for new hires.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (16, 'New Menu Item Tasting Scorecard', 4, 'A form for R&D to score and gather feedback on potential new menu items.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 17:43:45.692631+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (17, 'New Patient Registration Form', 1, 'Collects information for new patients visiting Sozo Skin clinics.', '2025-06-12 17:55:29.777587+07', 'system_setup', '2025-06-12 17:55:29.777587+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (18, 'Daily Clinic Revenue Summary', 2, 'A daily summary of revenue, patient count, and procedures for a dental clinic.', '2025-06-12 17:55:29.777587+07', 'system_setup', '2025-06-12 17:55:29.777587+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (19, 'Post-Training Feedback Survey', 3, 'A survey to be filled out by attendees after completing a training session.', '2025-06-12 17:55:29.777587+07', 'system_setup', '2025-06-12 17:55:29.777587+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (1, 'Treatment Procedure Log', 1, 'Records the details of treatments performed on a patient - Updated description', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-12 22:55:45.208366+07', 'superadmin', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (20, 'Daily Outlet Sales & Stock Report', 4, 'End-of-day report for sales figures and key inventory levels for a restaurant outlet.', '2025-06-12 17:55:29.777587+07', 'system_setup', '2025-06-12 22:56:05.443371+07', 'system_setup', '2025-06-12 22:56:05.44497+07', 'superadmin', true, true);
INSERT INTO public.data_inputs VALUES (2, 'Skincare Product Inventory', 1, 'Tracks stock levels for retail skincare products sold at clinics.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-16 21:23:10.911415+07', 'system_setup', '2025-06-16 21:23:10.917598+07', 'ipman', true, true);
INSERT INTO public.data_inputs VALUES (7, 'Insurance Claim Submission Form', 2, 'Form to collect all necessary details for submitting an insurance claim.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-16 21:50:11.379002+07', 'system_setup', '2025-06-16 21:50:11.381986+07', 'ipman', true, true);
INSERT INTO public.data_inputs VALUES (3, 'Social Media Campaign Tracker', 4, 'Monitors performance metrics and costs for social media ads.', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-16 21:51:16.123837+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (4, 'Monthly Consumables Expense Report', 1, 'Tracks monthly spending on clinical consumable goods..', '2025-06-12 17:43:45.692631+07', 'system_setup', '2025-06-16 21:51:52.361101+07', 'system_setup', NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (21, 'Tes SOZO Skin data input', 5, '', '2025-06-16 21:52:34.712015+07', NULL, '2025-06-16 22:01:13.840964+07', NULL, NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (22, 'New Sparks DI', 3, 'New sparks DI', '2025-06-16 22:03:30.905268+07', NULL, '2025-06-16 22:03:30.905268+07', NULL, NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (23, 'Schedule Tenaga Medis - Sozo Skin', 1, 'Schedule doctors, therapis, etc for SOZO Skin brand', '2025-06-17 19:31:36.908572+07', NULL, '2025-06-17 19:31:36.908572+07', NULL, NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (24, 'Test DI', 5, '', '2025-06-18 13:42:19.740569+07', NULL, '2025-06-18 13:42:19.740569+07', NULL, NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (25, 'Test date format DI', 8, '', '2025-06-23 05:44:55.437118+07', NULL, '2025-06-23 05:44:55.437118+07', NULL, NULL, NULL, false, true);
INSERT INTO public.data_inputs VALUES (26, 'New DI', 8, '', '2025-06-23 06:00:10.837664+07', NULL, '2025-06-23 06:00:10.837664+07', NULL, NULL, NULL, false, true);


