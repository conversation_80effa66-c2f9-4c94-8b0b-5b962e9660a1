import sys
sys.path.append('.')

def test_local_development():
    """Test the local development database and CRUD functionality"""
    try:
        # Test basic imports
        print("🔍 Testing Local Development...")

        from app.main import app
        print("✅ Successfully imported app")

        from app.core.database import get_db
        print("✅ Successfully imported database")

        from app.crud.user import user as crud_user
        print("✅ Successfully imported CRUD")

        from app.api.v1.endpoints.auth import serialize_user
        print("✅ Successfully imported auth endpoints")

        # Test database connection
        db = next(get_db())
        print("✅ Database connection successful")

        # Test authentication
        print("\n🔐 Testing Authentication...")
        user = crud_user.authenticate(db, username='admin', password='admin123')
        if user:
            print(f"✅ Authentication successful for: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Last login: {user.last_login}")
            print(f"   Login count: {user.login_count}")

            # Test get_with_roles
            print("\n👥 Testing Role Loading...")
            user_with_roles = crud_user.get_with_roles(db, id=user.id)
            if user_with_roles and user_with_roles.user_roles:
                for ur in user_with_roles.user_roles:
                    print(f"✅ Role: {ur.role.name if ur.role else 'No role'}")
            else:
                print("❌ No roles found for user")
                return False

            # Test serialization
            print("\n📋 Testing User Serialization...")
            serialized = serialize_user(user_with_roles)
            print(f"✅ Serialization successful:")
            print(f"   Username: {serialized.get('username')}")
            print(f"   Email: {serialized.get('email')}")
            print(f"   Role: {serialized.get('role')}")
            print(f"   Last login: {serialized.get('last_login')}")
            print(f"   Login count: {serialized.get('login_count')}")

            # Test search functionality
            print("\n🔍 Testing Search Functionality...")
            search_users = crud_user.get_multi_with_roles(db, search='admin', limit=5)
            print(f"✅ Search for 'admin' found {len(search_users)} users")
            for u in search_users:
                print(f"   - {u.username} ({u.email})")

            # Test all users
            print("\n👥 Testing All Users...")
            all_users = crud_user.get_multi_with_roles(db, limit=10)
            print(f"✅ Found {len(all_users)} users total")
            for u in all_users:
                role_name = None
                if u.user_roles:
                    role_name = u.user_roles[0].role.name if u.user_roles[0].role else None
                print(f"   - {u.username} ({u.email}) - Role: {role_name} - Last login: {u.last_login}")

            print("\n🎉 All local development tests passed!")
            print("\n📝 Summary:")
            print("   ✅ Database connection working")
            print("   ✅ Password authentication working")
            print("   ✅ Role loading working")
            print("   ✅ User serialization working")
            print("   ✅ Search functionality working")
            print("   ✅ Last login tracking working")

            return True

        else:
            print("❌ Authentication failed")
            return False

    except Exception as e:
        print(f"❌ Error during local development testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_local_development()
    if success:
        print("\n✅ Local development is working correctly!")
    else:
        print("\n❌ Local development has issues that need to be fixed.")
