import requests
import json
import sys

def test_api_endpoints():
    """Test the actual API endpoints"""
    base_url = "http://localhost:8000"
    
    try:
        print("🔍 Testing API Endpoints...")
        
        # Test health endpoint
        print("\n1. Testing Health Endpoint...")
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
            else:
                print(f"   Error: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Health endpoint failed: {e}")
            print("   Make sure the backend server is running on port 8000")
            return False
        
        # Test login endpoint
        print("\n2. Testing Login Endpoint...")
        login_data = {'username': 'admin', 'password': 'admin123'}
        try:
            response = requests.post(f"{base_url}/api/v1/auth/login/json", 
                                   json=login_data, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("   ✅ Login successful!")
                print(f"   Token: {result.get('access_token', 'N/A')[:20]}...")
                
                if 'user' in result:
                    user = result['user']
                    print(f"   User: {user.get('username')} ({user.get('email')})")
                    print(f"   Role: {user.get('role')}")
                    print(f"   Last login: {user.get('last_login')}")
                    print(f"   Login count: {user.get('login_count')}")
                    
                    # Get token for further tests
                    token = result.get('access_token')
                    headers = {'Authorization': f'Bearer {token}'}
                    
                    # Test users endpoint
                    print("\n3. Testing Users List Endpoint...")
                    try:
                        users_response = requests.get(f"{base_url}/api/v1/users/", 
                                                    headers=headers, timeout=10)
                        print(f"   Status: {users_response.status_code}")
                        
                        if users_response.status_code == 200:
                            users = users_response.json()
                            print(f"   ✅ Found {len(users)} users")
                            for user in users[:3]:  # Show first 3 users
                                print(f"     - {user.get('username')} ({user.get('email')}) - Role: {user.get('role')} - Last login: {user.get('last_login')}")
                        else:
                            print(f"   ❌ Users endpoint failed: {users_response.text}")
                            return False
                    except requests.exceptions.RequestException as e:
                        print(f"   ❌ Users endpoint failed: {e}")
                        return False
                    
                    # Test search endpoint
                    print("\n4. Testing Search Endpoint...")
                    try:
                        search_response = requests.get(f"{base_url}/api/v1/users/?search=admin", 
                                                     headers=headers, timeout=10)
                        print(f"   Status: {search_response.status_code}")
                        
                        if search_response.status_code == 200:
                            search_users = search_response.json()
                            print(f"   ✅ Search for 'admin' found {len(search_users)} users")
                            for user in search_users:
                                print(f"     - {user.get('username')} ({user.get('email')})")
                        else:
                            print(f"   ❌ Search endpoint failed: {search_response.text}")
                            return False
                    except requests.exceptions.RequestException as e:
                        print(f"   ❌ Search endpoint failed: {e}")
                        return False
                    
                    print("\n🎉 All API endpoint tests passed!")
                    return True
                    
                else:
                    print("   ❌ No user info in login response")
                    print(f"   Response keys: {list(result.keys())}")
                    return False
            else:
                print(f"   ❌ Login failed: {response.text}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Login endpoint failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error during API endpoint testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting API Endpoint Tests...")
    print("   Make sure the backend server is running: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print()
    
    success = test_api_endpoints()
    if success:
        print("\n✅ All API endpoints are working correctly!")
        print("\n📝 Summary:")
        print("   ✅ Health endpoint working")
        print("   ✅ Login endpoint working with role information")
        print("   ✅ Users list endpoint working with roles")
        print("   ✅ Search endpoint working")
        print("   ✅ Last login tracking working")
        print("\n🎯 Ready to push to repository for new Docker image!")
    else:
        print("\n❌ API endpoints have issues that need to be fixed.")
        print("   Please check if the backend server is running and try again.")
