# .github/workflows/frontend-docker-publish.yml

name: Docker Build and Push (Frontend)

on:
  push:
    branches: [ "main" ]
    paths:
      - 'frontend/**'
      - '.github/workflows/frontend-docker-publish.yml'

env:
  # Your GitHub organization name (all lowercase).
  ORG_NAME: seven-retail-group
  # The specific name for your frontend image.
  IMAGE_NAME: sr-internal-service-frontend

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ env.ORG_NAME }}/${{ env.IMAGE_NAME }}
          tags: |
            # This creates a tag with the specific git commit hash (e.g., a1b2c3d)
            # This is great for traceability and deploying specific versions.
            type=sha
            # This creates the 'latest' tag, but ONLY when a push happens to the default branch (main).
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          # Set the build context to the 'frontend' directory.
          context: ./frontend
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}