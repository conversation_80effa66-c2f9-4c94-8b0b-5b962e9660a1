# .github/workflows/docker-publish.yml

name: Docker Build and Push (Backend)

on:
  push:
    branches: [ "main" ]
    # Optional: Only run this workflow if changes are made in the 'backend/' directory
    paths:
      - 'backend/**'

env:
  # Your GitHub organization name (all lowercase).
  ORG_NAME: seven-retail-group
  
  # === THIS IS THE KEY CHANGE ===
  # Explicitly define the desired Docker image name here.
  # It does not need to match the repository name.
  IMAGE_NAME: sr-internal-service-backend

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          # This line now uses your specific IMAGE_NAME from the env section
          images: ghcr.io/${{ env.ORG_NAME }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          # If your Dockerfile is not in the root, specify its location with the 'file' parameter
          file: ./backend/Dockerfile
          context: ./backend #. Or ./backend if all backend code is in a subfolder
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}