import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  X, 
  Home, 
  Building2, 
  Database, 
  Users, 
  Settings,
  BarChart3,
  FileText,
  Shield
} from 'lucide-react'
import { useAuth } from '@/context/AuthContext'
import { ROUTES, USER_ROLES } from '@/utils/constants'

const Sidebar = ({ open, setOpen }) => {
  const location = useLocation()
  const { user, hasRole } = useAuth()

  const navigation = [
    { name: 'Dashboard', href: ROUTES.DASHBOARD, icon: Home },
    { name: 'Brands', href: ROUTES.BRANDS, icon: Building2 },
    { name: 'Data Inputs', href: ROUTES.DATA_INPUTS, icon: Database },
    { name: 'Reports', href: '/reports', icon: BarChart3 },
    { name: 'Audit Logs', href: '/audit-logs', icon: FileText },
  ]

  // Add admin-only navigation items
  if (hasRole(USER_ROLES.ADMIN)) {
    navigation.push(
      { name: 'Users', href: ROUTES.USERS, icon: Users },
      { name: 'System Settings', href: '/admin/settings', icon: Shield }
    )
  }

  navigation.push({ name: 'Settings', href: ROUTES.SETTINGS, icon: Settings })

  const isActive = (href) => {
    if (href === ROUTES.DASHBOARD) {
      return location.pathname === href
    }
    return location.pathname.startsWith(href)
  }

  return (
    <>
      {/* Mobile sidebar overlay */}
      {open && (
        <div className="fixed inset-0 z-40 lg:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setOpen(false)} />
        </div>
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0
        ${open ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex h-full flex-col">
          {/* Logo and close button */}
          <div className="flex h-16 items-center justify-between px-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-lg bg-primary-600 flex items-center justify-center">
                <Database className="h-5 w-5 text-white" />
              </div>
              <span className="ml-3 text-lg font-semibold text-gray-900">
                7Retail IS
              </span>
            </div>
            <button
              type="button"
              className="lg:hidden -mr-2 h-10 w-10 rounded-md flex items-center justify-center text-gray-500 hover:text-gray-900 hover:bg-gray-100"
              onClick={() => setOpen(false)}
            >
              <span className="sr-only">Close sidebar</span>
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* User info */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                <span className="text-sm font-medium text-primary-600">
                  {user?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {user?.full_name || user?.email}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {user?.is_superuser ? 'Admin' : (user?.role || 'User')}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href)
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${active
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                  onClick={() => setOpen(false)}
                >
                  <Icon
                    className={`
                      mr-3 h-5 w-5 flex-shrink-0
                      ${active ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <div>Version 1.0.0</div>
              <div className="mt-1">
                © 2025 SR Internal Service
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
