// API Configuration
// Use relative URL for production deployment, fallback to localhost for development
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? 'http://localhost:8000' : '')
export const API_PREFIX = '/api/v1'

// Authentication
export const TOKEN_KEY = 'sr_internal_token'
export const USER_KEY = 'sr_internal_user'
export const REFRESH_TOKEN_KEY = 'sr_internal_refresh_token'

// Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  
  // Brands
  BRANDS: '/brands',
  BRAND_DETAIL: '/brands/:id',
  BRAND_CREATE: '/brands/create',
  BRAND_EDIT: '/brands/:id/edit',
  
  // Data Inputs
  DATA_INPUTS: '/data-inputs',
  DATA_INPUT_DETAIL: '/data-inputs/:id',
  DATA_INPUT_CREATE: '/data-inputs/create',
  DATA_INPUT_EDIT: '/data-inputs/:id/edit',
  DATA_INPUT_RECORDS: '/data-inputs/:id/records',

  // Data Input Attributes
  ATTRIBUTE_DETAIL: '/data-inputs/:dataInputId/attributes/:attributeId',
  ATTRIBUTE_CREATE: '/data-inputs/:dataInputId/attributes/create',
  ATTRIBUTE_EDIT: '/data-inputs/:dataInputId/attributes/:attributeId/edit',

  // Data Input Records
  RECORD_DETAIL: '/data-inputs/:dataInputId/records/:recordId',
  RECORD_CREATE: '/data-inputs/:dataInputId/records/create',
  RECORD_EDIT: '/data-inputs/:dataInputId/records/:recordId/edit',
  RECORD_BULK_UPLOAD: '/data-inputs/:dataInputId/records/bulk-upload',
  
  // Users (Admin only)
  USERS: '/users',
  USER_DETAIL: '/users/:id',
  USER_CREATE: '/users/create',
  USER_EDIT: '/users/:id/edit',
  
  // Settings
  SETTINGS: '/settings',
}

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  VIEWER: 'viewer',
}

// Permissions
export const PERMISSIONS = {
  VIEW: 'view',
  CREATE: 'create',
  EDIT: 'edit',
  DELETE: 'delete',
  MANAGE_USERS: 'manage_users',
  APPROVE_REQUESTS: 'approve_requests',
}

// Data Types
export const DATA_TYPES = {
  TEXT: 'Text',
  NUMBER: 'Number',
  DECIMAL: 'Decimal',
  BOOLEAN: 'Boolean',
  DATE: 'Date',
  DATETIME: 'Datetime',
  EMAIL: 'Email',
  URL: 'URL',
  FILE: 'File',
  IMAGE: 'Image',
  VIDEO: 'Video',
  AUDIO: 'Audio',
}

// Validation Rules
export const VALIDATION_RULES = {
  REQUIRED: 'required',
  UNIQUE: 'unique',
  MIN_LENGTH: 'min_length',
  MAX_LENGTH: 'max_length',
  MIN_VALUE: 'min_value',
  MAX_VALUE: 'max_value',
  PATTERN: 'pattern',
  ALLOWED_VALUES: 'allowed_values',
  EMAIL: 'email',
  URL: 'url',
  DATE_FORMAT: 'date_format',
  FILE_EXTENSION: 'file_extension',
  FILE_SIZE: 'file_size',
}

// UI Constants
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
}

export const TOAST_MESSAGES = {
  LOGIN_SUCCESS: 'Welcome back!',
  LOGIN_ERROR: 'Invalid credentials. Please try again.',
  LOGOUT_SUCCESS: 'You have been logged out successfully.',
  SAVE_SUCCESS: 'Changes saved successfully!',
  DELETE_SUCCESS: 'Item deleted successfully.',
  ERROR_GENERIC: 'Something went wrong. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  VALIDATION_ERROR: 'Please check your input and try again.',
}

// Status Constants
export const STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
}

// Theme
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
}
