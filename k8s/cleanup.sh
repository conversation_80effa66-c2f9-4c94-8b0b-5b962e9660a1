#!/bin/bash

# SR Internal Service - Cleanup Script
# This script removes all resources from GKE

set -e

# Set paths
GCLOUD="/Users/<USER>/Work/gcloud-cli-software/google-cloud-sdk/bin/gcloud"
KUBECTL="/Users/<USER>/Work/gcloud-cli-software/google-cloud-sdk/bin/kubectl"

# Function to run kubectl with proper auth
run_kubectl() {
    export USE_GKE_GCLOUD_AUTH_PLUGIN=True
    export PATH="/Users/<USER>/Work/gcloud-cli-software/google-cloud-sdk/bin:$PATH"
    $KUBECTL "$@"
}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${PROJECT_ID:-"seven-retail-7"}
NAMESPACE="sr-internal-service"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

confirm_cleanup() {
    print_warning "This will delete ALL resources for SR Internal Service from Kubernetes!"
    print_warning "This action cannot be undone."
    echo
    read -p "Are you sure you want to continue? (type 'yes' to confirm): " confirmation
    
    if [ "$confirmation" != "yes" ]; then
        print_status "Cleanup cancelled."
        exit 0
    fi
}

cleanup_kubernetes_resources() {
    print_status "Cleaning up Kubernetes resources..."

    # Set up authentication
    export USE_GKE_GCLOUD_AUTH_PLUGIN=True

    # Delete in reverse order of dependencies (suppress monitoring errors)
    run_kubectl delete -f monitoring.yaml --ignore-not-found=true 2>/dev/null || print_warning "Monitoring resources not found (this is normal)"
    run_kubectl delete -f network-policy.yaml --ignore-not-found=true
    run_kubectl delete -f hpa.yaml --ignore-not-found=true
    run_kubectl delete -f ingress.yaml --ignore-not-found=true
    run_kubectl delete -f frontend.yaml --ignore-not-found=true
    run_kubectl delete -f backend.yaml --ignore-not-found=true
    run_kubectl delete -f postgresql.yaml --ignore-not-found=true
    run_kubectl delete -f rbac.yaml --ignore-not-found=true
    run_kubectl delete -f migration-scripts.yaml --ignore-not-found=true
    run_kubectl delete -f configmap.yaml --ignore-not-found=true
    run_kubectl delete -f secrets.yaml --ignore-not-found=true

    # Explicitly delete PVCs to ensure fresh database
    print_status "Deleting persistent volumes..."
    run_kubectl delete pvc --all -n $NAMESPACE --ignore-not-found=true

    # Delete the namespace (this will delete any remaining resources)
    run_kubectl delete namespace $NAMESPACE --ignore-not-found=true

    print_success "Kubernetes resources cleaned up"
}

cleanup_gcp_resources() {
    print_status "Cleaning up GCP resources..."
    
    # Delete static IP
    if gcloud compute addresses describe sr-internal-service-ip --global --project=$PROJECT_ID >/dev/null 2>&1; then
        print_status "Deleting static IP address..."
        gcloud compute addresses delete sr-internal-service-ip \
            --global \
            --project=$PROJECT_ID \
            --quiet
        print_success "Static IP address deleted"
    else
        print_warning "Static IP 'sr-internal-service-ip' not found"
    fi
    
    # Note: We don't delete the GKE cluster as it might be used for other services
    print_warning "GKE cluster not deleted. Delete manually if no longer needed:"
    echo "  gcloud container clusters delete sr-internal-service-cluster --region us-central1 --project $PROJECT_ID"
}

show_cleanup_status() {
    print_status "Cleanup Status:"
    echo
    
    print_status "Checking for remaining resources..."
    
    # Check if namespace still exists
    if run_kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
        print_warning "Namespace $NAMESPACE still exists"
        run_kubectl get all -n $NAMESPACE
    else
        print_success "Namespace $NAMESPACE successfully deleted"
    fi
    
    # Check static IP
    if gcloud compute addresses describe sr-internal-service-ip --global --project=$PROJECT_ID >/dev/null 2>&1; then
        print_warning "Static IP 'sr-internal-service-ip' still exists"
    else
        print_success "Static IP successfully deleted"
    fi
}

main() {
    print_status "SR Internal Service - Cleanup Script"
    echo
    
    confirm_cleanup
    cleanup_kubernetes_resources
    cleanup_gcp_resources
    show_cleanup_status
    
    print_success "Cleanup completed!"
    print_status "If you want to completely remove the GKE cluster, run:"
    echo "  gcloud container clusters delete sr-internal-service-cluster --region us-central1 --project $PROJECT_ID"
}

main "$@"
