# 🚀 SR Internal Service - GKE Deployment Checklist

## Pre-Deployment Checklist

### ✅ Prerequisites
- [ ] Google Cloud SDK installed and configured
- [ ] kubectl installed
- [ ] GKE cluster created and accessible
- [ ] GitHub Personal Access Token with package:read permissions
- [ ] Domain names registered and ready for DNS configuration

### ✅ Configuration Updates Required

#### 1. Update `k8s/secrets.yaml`
- [ ] Run `./encode-secrets.sh` to generate base64 encoded secrets
- [ ] Update `DATABASE_URL` with your PostgreSQL connection string
- [ ] Update `SECRET_KEY` with a strong secret key for JWT tokens
- [ ] Update `POSTGRES_DB`, `POSTGRES_USER`, `POSTGRES_PASSWORD` with your database credentials

#### 2. Update `k8s/configmap.yaml`
- [ ] Update `BACKEND_CORS_ORIGINS` with your actual frontend domain(s)
- [ ] Update `VITE_API_BASE_URL` with your actual backend domain

#### 3. Update `k8s/ingress.yaml`
- [ ] Replace `your-frontend-domain.com` with your actual frontend domain
- [ ] Replace `your-backend-domain.com` with your actual backend domain
- [ ] Update `kubernetes.io/ingress.global-static-ip-name` if using different IP name

#### 4. Update `k8s/deploy.sh`
- [ ] Set `PROJECT_ID` to your GCP project ID
- [ ] Set `CLUSTER_NAME` to your GKE cluster name
- [ ] Set `REGION` to your GKE cluster region
- [ ] Update GitHub username and email in the script

### ✅ Environment Variables
Set these before running deployment:
```bash
export PROJECT_ID="your-gcp-project-id"
export CLUSTER_NAME="sr-internal-service-cluster"
export REGION="us-central1"
export GITHUB_TOKEN="your-github-personal-access-token"
```

## Deployment Steps

### ✅ Step 1: Prepare Environment
- [ ] Clone repository and navigate to project root
- [ ] Ensure all configuration files are updated
- [ ] Verify GitHub Container Registry images are available:
  - `ghcr.io/seven-retail-group/sr-internal-service-backend:main`
  - `ghcr.io/seven-retail-group/sr-internal-service-frontend:main`

### ✅ Step 2: Deploy to GKE
Choose one of the following methods:

#### Option A: Automated Deployment
```bash
cd k8s
./deploy.sh
```

#### Option B: Manual Deployment
```bash
cd k8s

# Configure kubectl
gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION --project $PROJECT_ID

# Create GitHub registry secret
kubectl create secret docker-registry ghcr-secret \
    --docker-server=ghcr.io \
    --docker-username=your-github-username \
    --docker-password=$GITHUB_TOKEN \
    --docker-email=<EMAIL> \
    --namespace=sr-internal-service

# Create static IP
gcloud compute addresses create sr-internal-service-ip --global --project=$PROJECT_ID

# Deploy resources
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f configmap.yaml
kubectl apply -f rbac.yaml
kubectl apply -f postgresql.yaml

# Wait for PostgreSQL
kubectl wait --for=condition=ready pod -l app=postgresql -n sr-internal-service --timeout=300s

kubectl apply -f backend.yaml
kubectl apply -f frontend.yaml

# Wait for deployments
kubectl wait --for=condition=available deployment/backend -n sr-internal-service --timeout=300s
kubectl wait --for=condition=available deployment/frontend -n sr-internal-service --timeout=300s

kubectl apply -f ingress.yaml
kubectl apply -f hpa.yaml
kubectl apply -f network-policy.yaml
kubectl apply -f monitoring.yaml
```

### ✅ Step 3: Post-Deployment Configuration

#### DNS Configuration
- [ ] Get static IP address: `gcloud compute addresses describe sr-internal-service-ip --global --format="value(address)"`
- [ ] Create DNS A records:
  - `your-frontend-domain.com` → Static IP
  - `your-backend-domain.com` → Static IP
- [ ] Wait for DNS propagation (can take up to 48 hours)

#### SSL Certificate
- [ ] Verify managed certificate is provisioned (can take 10-60 minutes)
- [ ] Check certificate status: `kubectl describe managedcertificate sr-internal-service-ssl-cert -n sr-internal-service`

## Verification Checklist

### ✅ Health Checks
- [ ] All pods are running: `kubectl get pods -n sr-internal-service`
- [ ] All services are available: `kubectl get services -n sr-internal-service`
- [ ] Ingress has IP address: `kubectl get ingress -n sr-internal-service`
- [ ] HPA is active: `kubectl get hpa -n sr-internal-service`

### ✅ Application Testing
- [ ] Frontend loads at `https://your-frontend-domain.com`
- [ ] Backend API responds at `https://your-backend-domain.com/api/v1/health`
- [ ] Database connection works (check backend logs)
- [ ] Authentication flow works
- [ ] File upload functionality works
- [ ] All major features are functional

### ✅ Monitoring and Logging
- [ ] Check application logs: `kubectl logs -f deployment/backend -n sr-internal-service`
- [ ] Verify monitoring is working (if configured)
- [ ] Set up alerting (if required)

## Troubleshooting Common Issues

### ✅ Pod Issues
- [ ] Check pod status: `kubectl describe pod <pod-name> -n sr-internal-service`
- [ ] Check events: `kubectl get events -n sr-internal-service --sort-by='.lastTimestamp'`
- [ ] Check resource limits and requests

### ✅ Database Issues
- [ ] Verify PostgreSQL pod is running
- [ ] Check database connection string in secrets
- [ ] Verify database credentials
- [ ] Check if database migrations need to be run

### ✅ Networking Issues
- [ ] Verify services are accessible within cluster
- [ ] Check ingress configuration
- [ ] Verify DNS records are correct
- [ ] Check SSL certificate status

### ✅ Image Pull Issues
- [ ] Verify GitHub token has correct permissions
- [ ] Check if images exist in registry
- [ ] Verify image pull secret is correctly configured

## Maintenance Tasks

### ✅ Regular Maintenance
- [ ] Monitor resource usage and adjust limits if needed
- [ ] Update application images when new versions are available
- [ ] Backup database regularly
- [ ] Monitor SSL certificate expiration
- [ ] Review and update security policies

### ✅ Scaling
- [ ] Monitor HPA metrics and adjust thresholds if needed
- [ ] Consider vertical scaling for database if needed
- [ ] Monitor storage usage and expand if necessary

## Emergency Procedures

### ✅ Rollback
```bash
# Rollback to previous version
kubectl rollout undo deployment/backend -n sr-internal-service
kubectl rollout undo deployment/frontend -n sr-internal-service
```

### ✅ Complete Cleanup
```bash
cd k8s
./cleanup.sh
```

## Support Contacts
- **Infrastructure**: [Your Infrastructure Team]
- **Application**: [Your Development Team]
- **Database**: [Your Database Team]

---

**Last Updated**: $(date)
**Deployment Version**: 1.0.0
