apiVersion: v1
kind: ConfigMap
metadata:
  name: sr-internal-service-config
  namespace: sr-internal-service
data:
  # API Configuration
  API_V1_STR: "/api/v1"
  PROJECT_NAME: "SR Internal Service API"
  PROJECT_VERSION: "1.0.0"
  
  # Security
  ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "120"
  
  # CORS - Allow access from current ingress IP and localhost for development
  BACKEND_CORS_ORIGINS: '["http://localhost:3000", "http://localhost:8080", "http://*************"]'
  
  # File Storage
  UPLOAD_DIR: "uploads"
  MAX_FILE_SIZE: "52428800"  # 50MB
  STORAGE_BACKEND: "local"
  
  # Image Processing
  MAX_IMAGE_WIDTH: "4096"
  MAX_IMAGE_HEIGHT: "4096"
  IMAGE_QUALITY: "85"
  
  # File Security
  VIRUS_SCAN_ENABLED: "false"
  QUARANTINE_DIR: "quarantine"
  
  # Frontend Environment Variables (updated with ingress IP)
  VITE_API_BASE_URL: "http://*************/"  # Ingress external IP
  VITE_APP_NAME: "SR Internal Service"
  VITE_APP_VERSION: "1.0.0"
