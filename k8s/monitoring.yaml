apiVersion: v1
kind: ServiceMonitor
metadata:
  name: sr-internal-service-monitor
  namespace: sr-internal-service
  labels:
    app: sr-internal-service
spec:
  selector:
    matchLabels:
      app: backend
  endpoints:
  - port: http
    path: /metrics
    interval: 30s

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: sr-internal-service-alerts
  namespace: sr-internal-service
  labels:
    app: sr-internal-service
spec:
  groups:
  - name: sr-internal-service.rules
    rules:
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate detected"
        description: "Error rate is above 10% for 5 minutes"
    
    - alert: HighResponseTime
      expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High response time detected"
        description: "95th percentile response time is above 1 second"
    
    - alert: DatabaseConnectionFailure
      expr: up{job="postgresql"} == 0
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "Database connection failure"
        description: "PostgreSQL database is not responding"
    
    - alert: PodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Pod is crash looping"
        description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is crash looping"
