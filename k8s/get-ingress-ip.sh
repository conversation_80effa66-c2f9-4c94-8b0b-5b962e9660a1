#!/bin/bash

# Script to get current ingress IP and update ConfigMap
# This can be run as a CronJob to keep the IP updated

NAMESPACE="sr-internal-service"
INGRESS_NAME="sr-internal-service-ingress"
CONFIGMAP_NAME="sr-internal-service-config"

# Get current ingress IP
INGRESS_IP=$(kubectl get ingress $INGRESS_NAME -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

if [ -z "$INGRESS_IP" ]; then
    echo "Could not get ingress IP"
    exit 1
fi

echo "Current ingress IP: $INGRESS_IP"

# Update ConfigMap with current ingress IP
kubectl patch configmap $CONFIGMAP_NAME -n $NAMESPACE --type merge -p "{\"data\":{\"INGRESS_IP\":\"$INGRESS_IP\",\"VITE_API_BASE_URL\":\"http://$INGRESS_IP\"}}"

echo "ConfigMap updated with ingress IP: $INGRESS_IP"
