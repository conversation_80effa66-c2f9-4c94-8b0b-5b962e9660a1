#!/bin/bash

# SR Internal Service - GKE Deployment Script
# This script deploys the SR Internal Service to Google Kubernetes Engine

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${PROJECT_ID:-"seven-retail-7"}
CLUSTER_NAME=${CLUSTER_NAME:-"superset-gke"}
REGION=${REGION:-"us-central1"}
NAMESPACE="sr-internal-service"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists kubectl; then
        print_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    if ! command_exists gcloud; then
        print_error "gcloud CLI is not installed. Please install Google Cloud SDK first."
        exit 1
    fi
    
    # Check if authenticated with gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_error "Not authenticated with gcloud. Please run 'gcloud auth login' first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Configure kubectl for GKE cluster
configure_kubectl() {
    print_status "Configuring kubectl for GKE cluster..."
    
    gcloud container clusters get-credentials $CLUSTER_NAME \
        --region $REGION \
        --project $PROJECT_ID
    
    print_success "kubectl configured for cluster: $CLUSTER_NAME"
}

# Create GitHub Container Registry secret
create_ghcr_secret() {
    print_status "Creating GitHub Container Registry secret..."
    
    if [ -z "$GITHUB_TOKEN" ]; then
        print_warning "GITHUB_TOKEN environment variable not set."
        print_warning "Please set it with a GitHub Personal Access Token that has package:read permissions."
        read -p "Enter your GitHub token: " -s GITHUB_TOKEN
        echo
    fi
    
    kubectl create secret docker-registry ghcr-secret \
        --docker-server=ghcr.io \
        --docker-username=your-github-username \
        --docker-password=$GITHUB_TOKEN \
        --docker-email=<EMAIL> \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    print_success "GitHub Container Registry secret created"
}

# Create static IP address
create_static_ip() {
    print_status "Creating static IP address..."
    
    if gcloud compute addresses describe sr-internal-service-ip --global --project=$PROJECT_ID >/dev/null 2>&1; then
        print_warning "Static IP 'sr-internal-service-ip' already exists"
        IP_ADDRESS=$(gcloud compute addresses describe sr-internal-service-ip --global --project=$PROJECT_ID --format="value(address)")
        print_status "Static IP address: $IP_ADDRESS"
    else
        gcloud compute addresses create sr-internal-service-ip \
            --global \
            --project=$PROJECT_ID
        
        IP_ADDRESS=$(gcloud compute addresses describe sr-internal-service-ip --global --project=$PROJECT_ID --format="value(address)")
        print_success "Static IP created: $IP_ADDRESS"
    fi
}

# Deploy Kubernetes resources
deploy_resources() {
    print_status "Deploying Kubernetes resources..."

    # Apply resources in order
    kubectl apply -f namespace.yaml
    kubectl apply -f secrets.yaml
    kubectl apply -f configmap.yaml
    kubectl apply -f migration-scripts.yaml
    kubectl apply -f rbac.yaml
    kubectl apply -f postgresql.yaml

    # Wait for PostgreSQL to be ready
    print_status "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgresql -n $NAMESPACE --timeout=300s

    # Deploy backend and frontend
    kubectl apply -f backend.yaml
    kubectl apply -f frontend.yaml

    # Wait for deployments to be ready
    print_status "Waiting for backend deployment to be ready..."
    kubectl wait --for=condition=available deployment/backend -n $NAMESPACE --timeout=300s

    print_status "Waiting for frontend deployment to be ready..."
    kubectl wait --for=condition=available deployment/frontend -n $NAMESPACE --timeout=300s

    # Apply remaining resources
    kubectl apply -f ingress.yaml
    kubectl apply -f hpa.yaml
    kubectl apply -f network-policy.yaml

    print_success "All resources deployed successfully"
}

# Update ConfigMap with static IP
update_configmap_with_ip() {
    if [ ! -z "$IP_ADDRESS" ]; then
        print_status "Updating ConfigMap with static IP address..."

        # Update CORS origins to include the static IP
        kubectl patch configmap sr-internal-service-config -n $NAMESPACE --type merge -p "{\"data\":{\"BACKEND_CORS_ORIGINS\":\"[\\\"http://$IP_ADDRESS\\\", \\\"http://localhost:3000\\\"]\"}}"

        # Update frontend API base URL
        kubectl patch configmap sr-internal-service-config -n $NAMESPACE --type merge -p "{\"data\":{\"VITE_API_BASE_URL\":\"http://$IP_ADDRESS\"}}"

        # Restart frontend deployment to pick up new config
        kubectl rollout restart deployment/frontend -n $NAMESPACE
        kubectl rollout restart deployment/backend -n $NAMESPACE

        print_success "ConfigMap updated with static IP: $IP_ADDRESS"
    fi
}

# Display deployment status
show_status() {
    print_status "Deployment Status:"
    echo

    print_status "Pods:"
    kubectl get pods -n $NAMESPACE
    echo

    print_status "Services:"
    kubectl get services -n $NAMESPACE
    echo

    print_status "Ingress:"
    kubectl get ingress -n $NAMESPACE
    echo

    print_status "HPA Status:"
    kubectl get hpa -n $NAMESPACE
    echo

    if [ ! -z "$IP_ADDRESS" ]; then
        print_success "🎉 Your application is available at:"
        echo "  Frontend: http://$IP_ADDRESS"
        echo "  Backend API: http://$IP_ADDRESS/api/v1"
        echo "  API Documentation: http://$IP_ADDRESS/docs"
        echo "  Alternative API Docs: http://$IP_ADDRESS/redoc"
        echo
        print_status "Static IP Address: $IP_ADDRESS"
        print_warning "This is a staging deployment without SSL/DNS configuration"
        print_warning "For production, configure proper domain names and SSL certificates"
    fi
}

# Main deployment function
main() {
    print_status "Starting SR Internal Service deployment to GKE..."
    echo
    
    check_prerequisites
    configure_kubectl
    create_static_ip
    create_ghcr_secret
    deploy_resources
    update_configmap_with_ip
    show_status
    
    print_success "Deployment completed successfully!"
    print_warning "Next steps:"
    echo "  1. Access your application at: http://$IP_ADDRESS"
    echo "  2. Check the API documentation at: http://$IP_ADDRESS/docs"
    echo "  3. Monitor the deployment with: kubectl get pods -n $NAMESPACE -w"
    echo "  4. Check logs with: kubectl logs -f deployment/backend -n $NAMESPACE"
    echo "  5. For production deployment, configure proper DNS and SSL certificates"
}

# Run main function
main "$@"
