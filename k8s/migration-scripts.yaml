apiVersion: v1
kind: ConfigMap
metadata:
  name: migration-scripts
  namespace: sr-internal-service
data:
  # Soft delete migration
  add_soft_delete_columns.sql: |
    -- Migration to add soft delete columns to data_inputs and records tables
    -- Run this script to add the necessary columns for soft delete functionality

    -- Add soft delete columns to data_inputs table
    ALTER TABLE data_inputs 
    ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS deleted_by <PERSON><PERSON><PERSON><PERSON>(255),
    ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

    -- Add soft delete columns to records table  
    ALTER TABLE records
    ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS deleted_by <PERSON><PERSON><PERSON><PERSON>(255),
    ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

    -- Create indexes for better performance on soft delete queries
    CREATE INDEX IF NOT EXISTS idx_data_inputs_is_deleted ON data_inputs(is_deleted);
    CREATE INDEX IF NOT EXISTS idx_records_is_deleted ON records(is_deleted);

  # Brand access control migration
  add_brand_access_control.sql: |
    -- Create brand access control tables and relationships
    
    -- Create brands table if not exists
    CREATE TABLE IF NOT EXISTS brands (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by VARCHAR(255),
        updated_by VARCHAR(255)
    );

    -- Create brand access table if not exists
    CREATE TABLE IF NOT EXISTS user_brand_access (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        brand_id INTEGER NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
        access_level VARCHAR(50) DEFAULT 'read',
        granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        granted_by VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        UNIQUE(user_id, brand_id)
    );

    -- Add brand_id to data_inputs if not exists
    ALTER TABLE data_inputs 
    ADD COLUMN IF NOT EXISTS brand_id INTEGER REFERENCES brands(id);

    -- Add brand_id to records if not exists  
    ALTER TABLE records
    ADD COLUMN IF NOT EXISTS brand_id INTEGER REFERENCES brands(id);

    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_user_brand_access_user_id ON user_brand_access(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_brand_access_brand_id ON user_brand_access(brand_id);
    CREATE INDEX IF NOT EXISTS idx_data_inputs_brand_id ON data_inputs(brand_id);
    CREATE INDEX IF NOT EXISTS idx_records_brand_id ON records(brand_id);

  # Initialize basic data
  init_basic_data.sql: |
    -- Insert default brands if they don't exist
    INSERT INTO brands (name, description, created_by, updated_by)
    SELECT 'Default Brand', 'Default brand for system', 'system', 'system'
    WHERE NOT EXISTS (SELECT 1 FROM brands WHERE name = 'Default Brand');

    INSERT INTO brands (name, description, created_by, updated_by)
    SELECT 'Seven Retail', 'Seven Retail Group brand', 'system', 'system'
    WHERE NOT EXISTS (SELECT 1 FROM brands WHERE name = 'Seven Retail');

    -- Create roles if they don't exist
    CREATE TABLE IF NOT EXISTS roles (
        id SERIAL PRIMARY KEY,
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by VARCHAR(255),
        updated_by VARCHAR(255)
    );

    INSERT INTO roles (name, description, created_by, updated_by)
    SELECT 'admin', 'Administrator role', 'system', 'system'
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'admin');

    INSERT INTO roles (name, description, created_by, updated_by)
    SELECT 'user', 'Regular user role', 'system', 'system'
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'user');

    INSERT INTO roles (name, description, created_by, updated_by)
    SELECT 'manager', 'Manager role', 'system', 'system'
    WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'manager');
