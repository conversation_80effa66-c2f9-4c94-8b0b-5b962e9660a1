apiVersion: v1
kind: ConfigMap
metadata:
  name: migration-scripts
  namespace: sr-internal-service
data:
  # Database restoration script
  restore_database.sql: |
    -- Create the database structure from your local development database

    -- Create data_type_enum
    CREATE TYPE data_type_enum AS ENUM (
        'Text',
        'Number',
        'Decimal',
        'Boolean',
        'Date',
        'Datetime',
        'File',
        'Image',
        'Video',
        'Audio',
        'URL',
        'Email'
    );

    -- Create audit log function
    CREATE OR REPLACE FUNCTION log_changes() RETURNS trigger
        LANGUAGE plpgsql
        AS $$
    DECLARE
        v_old_data jsonb;
        v_new_data jsonb;
    BEGIN
        IF (TG_OP = 'UPDATE') THEN
            v_old_data = to_jsonb(OLD);
            v_new_data = to_jsonb(NEW);
            INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, new_data, query)
            VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'U', v_old_data, v_new_data, current_query());
            RETURN NEW;
        ELSIF (TG_OP = 'DELETE') THEN
            v_old_data = to_jsonb(OLD);
            INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, query)
            VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'D', v_old_data, current_query());
            RETURN OLD;
        ELSIF (TG_OP = 'INSERT') THEN
            v_new_data = to_jsonb(NEW);
            INSERT INTO audit_log (schema_name, table_name, user_name, action, new_data, query)
            VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'I', v_new_data, current_query());
            RETURN NEW;
        END IF;
        RETURN NULL;
    END;
    $$;

  # Create all tables from your database dump
  create_tables.sql: |
    -- Create audit_log table
    CREATE TABLE audit_log (
        id bigint NOT NULL,
        schema_name text NOT NULL,
        table_name text NOT NULL,
        user_name text,
        action_timestamp timestamp with time zone DEFAULT now() NOT NULL,
        action text NOT NULL,
        original_data jsonb,
        new_data jsonb,
        query text,
        CONSTRAINT audit_log_action_check CHECK ((action = ANY (ARRAY['I'::text, 'D'::text, 'U'::text])))
    );

    -- Create brands table
    CREATE TABLE brands (
        id integer NOT NULL,
        name character varying(255) NOT NULL,
        description character varying(255),
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        is_active boolean DEFAULT true
    );

    -- Create data_input_attribute_pending_requests table
    CREATE TABLE data_input_attribute_pending_requests (
        id integer NOT NULL,
        data_input_id integer,
        name character varying(100) NOT NULL,
        description character varying(255),
        request_status character varying(50) NOT NULL,
        request_message character varying(255),
        data_type character varying(50) NOT NULL,
        is_required boolean DEFAULT false,
        is_unique boolean DEFAULT false,
        is_active boolean DEFAULT true,
        version integer DEFAULT 1,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        format character varying(255),
        value_options jsonb
    );

    -- Create data_input_attributes table
    CREATE TABLE data_input_attributes (
        id integer NOT NULL,
        data_input_id integer NOT NULL,
        name character varying(100) NOT NULL,
        description character varying(255),
        data_type data_type_enum DEFAULT 'Text'::data_type_enum NOT NULL,
        is_required boolean DEFAULT false,
        is_unique boolean DEFAULT false,
        is_active boolean DEFAULT true,
        version integer DEFAULT 1,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        format character varying(255),
        value_options jsonb
    );

    -- Create data_input_pending_requests table
    CREATE TABLE data_input_pending_requests (
        id integer NOT NULL,
        brand_id integer NOT NULL,
        name character varying(100) NOT NULL,
        description character varying(255),
        request_status character varying(50) NOT NULL,
        request_message character varying(255),
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create data_inputs table
    CREATE TABLE data_inputs (
        id integer NOT NULL,
        name character varying(100) NOT NULL,
        brand_id integer NOT NULL,
        description character varying(255),
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        deleted_at timestamp with time zone,
        deleted_by character varying(255),
        is_deleted boolean DEFAULT false,
        is_active boolean DEFAULT true
    );

    -- Create records table
    CREATE TABLE records (
        id integer NOT NULL,
        data_input_id integer NOT NULL,
        value jsonb,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create roles table
    CREATE TABLE roles (
        id integer NOT NULL,
        name character varying(64) NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create user_brands table
    CREATE TABLE user_brands (
        id integer NOT NULL,
        user_id integer NOT NULL,
        brand_id integer NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        brand_role character varying(50),
        can_view boolean DEFAULT true,
        can_create boolean DEFAULT false,
        can_edit boolean DEFAULT false,
        can_delete boolean DEFAULT false,
        can_export boolean DEFAULT false,
        can_manage_users boolean DEFAULT false,
        can_manage_attributes boolean DEFAULT false
    );

    -- Create user_roles table
    CREATE TABLE user_roles (
        id integer NOT NULL,
        user_id integer NOT NULL,
        role_id integer NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create users table
    CREATE TABLE users (
        id integer NOT NULL,
        first_name character varying(64) NOT NULL,
        last_name character varying(64) NOT NULL,
        username character varying(64) NOT NULL,
        password character varying(256),
        email character varying(256) NOT NULL,
        active boolean,
        is_superuser boolean,
        last_login timestamp with time zone,
        login_count integer,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

  # Create sequences and constraints
  create_sequences_constraints.sql: |
    -- Create sequences
    CREATE SEQUENCE audit_log_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE brands_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_input_attribute_pending_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_input_attributes_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_input_pending_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_inputs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE records_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE roles_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE user_brands_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE user_roles_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE users_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;

    -- Set sequence ownership
    ALTER SEQUENCE audit_log_id_seq OWNED BY audit_log.id;
    ALTER SEQUENCE brands_id_seq OWNED BY brands.id;
    ALTER SEQUENCE data_input_attribute_pending_requests_id_seq OWNED BY data_input_attribute_pending_requests.id;
    ALTER SEQUENCE data_input_attributes_id_seq OWNED BY data_input_attributes.id;
    ALTER SEQUENCE data_input_pending_requests_id_seq OWNED BY data_input_pending_requests.id;
    ALTER SEQUENCE data_inputs_id_seq OWNED BY data_inputs.id;
    ALTER SEQUENCE records_id_seq OWNED BY records.id;
    ALTER SEQUENCE roles_id_seq OWNED BY roles.id;
    ALTER SEQUENCE user_brands_id_seq OWNED BY user_brands.id;
    ALTER SEQUENCE user_roles_id_seq OWNED BY user_roles.id;
    ALTER SEQUENCE users_id_seq OWNED BY users.id;

    -- Set default values for sequences
    ALTER TABLE ONLY audit_log ALTER COLUMN id SET DEFAULT nextval('audit_log_id_seq'::regclass);
    ALTER TABLE ONLY brands ALTER COLUMN id SET DEFAULT nextval('brands_id_seq'::regclass);
    ALTER TABLE ONLY data_input_attribute_pending_requests ALTER COLUMN id SET DEFAULT nextval('data_input_attribute_pending_requests_id_seq'::regclass);
    ALTER TABLE ONLY data_input_attributes ALTER COLUMN id SET DEFAULT nextval('data_input_attributes_id_seq'::regclass);
    ALTER TABLE ONLY data_input_pending_requests ALTER COLUMN id SET DEFAULT nextval('data_input_pending_requests_id_seq'::regclass);
    ALTER TABLE ONLY data_inputs ALTER COLUMN id SET DEFAULT nextval('data_inputs_id_seq'::regclass);
    ALTER TABLE ONLY records ALTER COLUMN id SET DEFAULT nextval('records_id_seq'::regclass);
    ALTER TABLE ONLY roles ALTER COLUMN id SET DEFAULT nextval('roles_id_seq'::regclass);
    ALTER TABLE ONLY user_brands ALTER COLUMN id SET DEFAULT nextval('user_brands_id_seq'::regclass);
    ALTER TABLE ONLY user_roles ALTER COLUMN id SET DEFAULT nextval('user_roles_id_seq'::regclass);
    ALTER TABLE ONLY users ALTER COLUMN id SET DEFAULT nextval('users_id_seq'::regclass);

    -- Add primary keys
    ALTER TABLE ONLY audit_log ADD CONSTRAINT audit_log_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY brands ADD CONSTRAINT brands_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_input_attribute_pending_requests ADD CONSTRAINT data_input_attribute_pending_requests_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_input_attributes ADD CONSTRAINT data_input_attributes_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_input_pending_requests ADD CONSTRAINT data_input_pending_requests_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_inputs ADD CONSTRAINT data_inputs_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY records ADD CONSTRAINT records_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY roles ADD CONSTRAINT roles_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY users ADD CONSTRAINT users_pkey PRIMARY KEY (id);

    -- Add unique constraints
    ALTER TABLE ONLY brands ADD CONSTRAINT brands_name_key UNIQUE (name);
    ALTER TABLE ONLY roles ADD CONSTRAINT roles_name_key UNIQUE (name);
    ALTER TABLE ONLY users ADD CONSTRAINT users_email_key UNIQUE (email);
    ALTER TABLE ONLY users ADD CONSTRAINT users_username_key UNIQUE (username);

    -- Add foreign key constraints
    ALTER TABLE ONLY data_input_attribute_pending_requests ADD CONSTRAINT data_input_attribute_pending_requests_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
    ALTER TABLE ONLY data_input_attributes ADD CONSTRAINT data_input_attributes_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
    ALTER TABLE ONLY data_input_pending_requests ADD CONSTRAINT data_input_pending_requests_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
    ALTER TABLE ONLY data_inputs ADD CONSTRAINT data_inputs_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
    ALTER TABLE ONLY records ADD CONSTRAINT records_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
    ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
    ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
    ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES roles(id);
    ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);

  # Initialize data from .dat files - EXACT DATA FROM LOCAL DEVELOPMENT DATABASE
  init_basic_data.sql: |
    -- Insert roles from 4370.dat - EXACT DATA
    INSERT INTO public.roles VALUES (1, 'Super Admin', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (2, 'Brand Admin', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (3, 'Data Architect', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (4, 'Data Editor', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (5, 'Data Entry', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (6, 'Viewer', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (7, 'admin', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (8, 'user', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.roles VALUES (9, 'manager', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');

    -- Insert brands from 4376.dat - EXACT DATA WITH is_active COLUMN
    INSERT INTO public.brands VALUES (1, 'SOZO Skin', 'Premium skincare and beauty treatments', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (2, 'Sozo Dental', 'Comprehensive dental care services', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (3, 'Sparks', 'Professional training and development', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (4, 'Golden Lamian', 'Authentic Asian cuisine restaurant chain', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (5, 'Jiwa Maternity', 'Maternity and childcare services', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (6, 'Load Test Brand', 'Brand for load testing purposes', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (7, 'Test Brand', 'Brand for testing purposes', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);
    INSERT INTO public.brands VALUES (8, 'Debug Test Brand', 'Brand for debugging and testing', '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup', true);

    -- Insert users from 4372.dat - EXACT DATA WITH REAL HASHED PASSWORDS
    INSERT INTO public.users VALUES (7, 'Super', 'Admin', 'superadmin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflHQrxK', '<EMAIL>', true, true, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (9, 'IP', 'Man', 'ipman', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (13, 'Sonya', 'M', 'sonyam', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (5, 'Raymond', 'User', 'raymond', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (1, 'Admin', 'User', 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflHQrxK', '<EMAIL>', true, true, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (4, 'Raihan', 'Dimas', 'raihandimas', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (3, 'Gema', 'Drakel', 'gema.drakel', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (2, 'Raja', 'Anugrah', 'raja.anugrah', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (6, 'Test', 'User', 'testuser', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (10, 'Load Test', 'User 1', 'loadtest_user_1', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (8, 'Manager', 'User', 'manager', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (11, 'Load Test', 'User 2', 'loadtest_user_2', '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true, false, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.users VALUES (12, 'Load Test', 'Admin', 'loadtest_admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflHQrxK', '<EMAIL>', true, true, NULL, 0, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');

    -- Insert user_roles from 4374.dat - EXACT DATA
    INSERT INTO public.user_roles VALUES (1, 1, 1, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (2, 2, 2, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (3, 3, 2, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (4, 4, 2, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (5, 5, 2, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (6, 7, 1, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (7, 9, 1, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');
    INSERT INTO public.user_roles VALUES (8, 13, 7, '2025-06-12 17:38:44.559734+07', 'system_setup', '2025-06-12 17:38:44.559734+07', 'system_setup');

    -- Insert user_brands from 4378.dat - EXACT DATA WITH ALL COLUMNS
    INSERT INTO public.user_brands VALUES (1, 2, 1, '2025-06-12 17:07:01.323111+07', 'system_setup', '2025-06-13 14:46:29.222611+07', 'system_setup', 'viewer', true, true, false, false, false, false, false);
    INSERT INTO public.user_brands VALUES (3, 3, 3, '2025-06-12 17:10:21.525724+07', 'system_setup', '2025-06-13 14:46:29.222611+07', 'system_setup', 'viewer', true, true, false, false, false, false, false);
    INSERT INTO public.user_brands VALUES (4, 4, 2, '2025-06-12 17:10:21.525724+07', 'system_setup', '2025-06-13 14:46:29.222611+07', 'system_setup', 'viewer', true, true, false, false, false, false, false);
    INSERT INTO public.user_brands VALUES (5, 5, 4, '2025-06-12 17:10:21.525724+07', 'system_setup', '2025-06-13 14:46:29.222611+07', 'system_setup', 'viewer', true, true, false, false, false, false, false);

    -- Update sequences to match the imported data from .dat files
    SELECT setval('roles_id_seq', (SELECT MAX(id) FROM roles));
    SELECT setval('brands_id_seq', (SELECT MAX(id) FROM brands));
    SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));
    SELECT setval('user_roles_id_seq', (SELECT MAX(id) FROM user_roles));
    SELECT setval('user_brands_id_seq', (SELECT MAX(id) FROM user_brands));
