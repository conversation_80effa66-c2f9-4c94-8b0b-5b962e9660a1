apiVersion: v1
kind: ConfigMap
metadata:
  name: migration-scripts
  namespace: sr-internal-service
data:
  # Database restoration script
  restore_database.sql: |
    -- Create the database structure from your local development database

    -- Create data_type_enum
    CREATE TYPE data_type_enum AS ENUM (
        'Text',
        'Number',
        'Decimal',
        'Boolean',
        'Date',
        'Datetime',
        'File',
        'Image',
        'Video',
        'Audio',
        'URL',
        'Email'
    );

    -- Create audit log function
    CREATE OR REPLACE FUNCTION log_changes() RETURNS trigger
        LANGUAGE plpgsql
        AS $$
    DECLARE
        v_old_data jsonb;
        v_new_data jsonb;
    BEGIN
        IF (TG_OP = 'UPDATE') THEN
            v_old_data = to_jsonb(OLD);
            v_new_data = to_jsonb(NEW);
            INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, new_data, query)
            VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'U', v_old_data, v_new_data, current_query());
            RETURN NEW;
        ELSIF (TG_OP = 'DELETE') THEN
            v_old_data = to_jsonb(OLD);
            INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, query)
            VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'D', v_old_data, current_query());
            RETURN OLD;
        ELSIF (TG_OP = 'INSERT') THEN
            v_new_data = to_jsonb(NEW);
            INSERT INTO audit_log (schema_name, table_name, user_name, action, new_data, query)
            VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'I', v_new_data, current_query());
            RETURN NEW;
        END IF;
        RETURN NULL;
    END;
    $$;

  # Create all tables from your database dump
  create_tables.sql: |
    -- Create audit_log table
    CREATE TABLE audit_log (
        id bigint NOT NULL,
        schema_name text NOT NULL,
        table_name text NOT NULL,
        user_name text,
        action_timestamp timestamp with time zone DEFAULT now() NOT NULL,
        action text NOT NULL,
        original_data jsonb,
        new_data jsonb,
        query text,
        CONSTRAINT audit_log_action_check CHECK ((action = ANY (ARRAY['I'::text, 'D'::text, 'U'::text])))
    );

    -- Create brands table
    CREATE TABLE brands (
        id integer NOT NULL,
        name character varying(255) NOT NULL,
        description character varying(255),
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create data_input_attribute_pending_requests table
    CREATE TABLE data_input_attribute_pending_requests (
        id integer NOT NULL,
        data_input_id integer,
        name character varying(100) NOT NULL,
        description character varying(255),
        request_status character varying(50) NOT NULL,
        request_message character varying(255),
        data_type character varying(50) NOT NULL,
        is_required boolean DEFAULT false,
        is_unique boolean DEFAULT false,
        is_active boolean DEFAULT true,
        version integer DEFAULT 1,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        format character varying(255),
        value_options jsonb
    );

    -- Create data_input_attributes table
    CREATE TABLE data_input_attributes (
        id integer NOT NULL,
        data_input_id integer NOT NULL,
        name character varying(100) NOT NULL,
        description character varying(255),
        data_type data_type_enum DEFAULT 'Text'::data_type_enum NOT NULL,
        is_required boolean DEFAULT false,
        is_unique boolean DEFAULT false,
        is_active boolean DEFAULT true,
        version integer DEFAULT 1,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255),
        format character varying(255),
        value_options jsonb
    );

    -- Create data_input_pending_requests table
    CREATE TABLE data_input_pending_requests (
        id integer NOT NULL,
        brand_id integer NOT NULL,
        name character varying(100) NOT NULL,
        description character varying(255),
        request_status character varying(50) NOT NULL,
        request_message character varying(255),
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create data_inputs table
    CREATE TABLE data_inputs (
        id integer NOT NULL,
        name character varying(100) NOT NULL,
        brand_id integer NOT NULL,
        description character varying(255),
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create records table
    CREATE TABLE records (
        id integer NOT NULL,
        data_input_id integer NOT NULL,
        value jsonb,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create roles table
    CREATE TABLE roles (
        id integer NOT NULL,
        name character varying(64) NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create user_brands table
    CREATE TABLE user_brands (
        id integer NOT NULL,
        user_id integer NOT NULL,
        brand_id integer NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create user_roles table
    CREATE TABLE user_roles (
        id integer NOT NULL,
        user_id integer NOT NULL,
        role_id integer NOT NULL,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

    -- Create users table
    CREATE TABLE users (
        id integer NOT NULL,
        first_name character varying(64) NOT NULL,
        last_name character varying(64) NOT NULL,
        username character varying(64) NOT NULL,
        password character varying(256),
        email character varying(256) NOT NULL,
        active boolean,
        is_superuser boolean,
        last_login timestamp with time zone,
        login_count integer,
        created_at timestamp with time zone DEFAULT now() NOT NULL,
        created_by character varying(255),
        updated_at timestamp with time zone DEFAULT now() NOT NULL,
        updated_by character varying(255)
    );

  # Create sequences and constraints
  create_sequences_constraints.sql: |
    -- Create sequences
    CREATE SEQUENCE audit_log_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE brands_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_input_attribute_pending_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_input_attributes_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_input_pending_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE data_inputs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE records_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE roles_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE user_brands_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE user_roles_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
    CREATE SEQUENCE users_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;

    -- Set sequence ownership
    ALTER SEQUENCE audit_log_id_seq OWNED BY audit_log.id;
    ALTER SEQUENCE brands_id_seq OWNED BY brands.id;
    ALTER SEQUENCE data_input_attribute_pending_requests_id_seq OWNED BY data_input_attribute_pending_requests.id;
    ALTER SEQUENCE data_input_attributes_id_seq OWNED BY data_input_attributes.id;
    ALTER SEQUENCE data_input_pending_requests_id_seq OWNED BY data_input_pending_requests.id;
    ALTER SEQUENCE data_inputs_id_seq OWNED BY data_inputs.id;
    ALTER SEQUENCE records_id_seq OWNED BY records.id;
    ALTER SEQUENCE roles_id_seq OWNED BY roles.id;
    ALTER SEQUENCE user_brands_id_seq OWNED BY user_brands.id;
    ALTER SEQUENCE user_roles_id_seq OWNED BY user_roles.id;
    ALTER SEQUENCE users_id_seq OWNED BY users.id;

    -- Set default values for sequences
    ALTER TABLE ONLY audit_log ALTER COLUMN id SET DEFAULT nextval('audit_log_id_seq'::regclass);
    ALTER TABLE ONLY brands ALTER COLUMN id SET DEFAULT nextval('brands_id_seq'::regclass);
    ALTER TABLE ONLY data_input_attribute_pending_requests ALTER COLUMN id SET DEFAULT nextval('data_input_attribute_pending_requests_id_seq'::regclass);
    ALTER TABLE ONLY data_input_attributes ALTER COLUMN id SET DEFAULT nextval('data_input_attributes_id_seq'::regclass);
    ALTER TABLE ONLY data_input_pending_requests ALTER COLUMN id SET DEFAULT nextval('data_input_pending_requests_id_seq'::regclass);
    ALTER TABLE ONLY data_inputs ALTER COLUMN id SET DEFAULT nextval('data_inputs_id_seq'::regclass);
    ALTER TABLE ONLY records ALTER COLUMN id SET DEFAULT nextval('records_id_seq'::regclass);
    ALTER TABLE ONLY roles ALTER COLUMN id SET DEFAULT nextval('roles_id_seq'::regclass);
    ALTER TABLE ONLY user_brands ALTER COLUMN id SET DEFAULT nextval('user_brands_id_seq'::regclass);
    ALTER TABLE ONLY user_roles ALTER COLUMN id SET DEFAULT nextval('user_roles_id_seq'::regclass);
    ALTER TABLE ONLY users ALTER COLUMN id SET DEFAULT nextval('users_id_seq'::regclass);

    -- Add primary keys
    ALTER TABLE ONLY audit_log ADD CONSTRAINT audit_log_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY brands ADD CONSTRAINT brands_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_input_attribute_pending_requests ADD CONSTRAINT data_input_attribute_pending_requests_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_input_attributes ADD CONSTRAINT data_input_attributes_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_input_pending_requests ADD CONSTRAINT data_input_pending_requests_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY data_inputs ADD CONSTRAINT data_inputs_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY records ADD CONSTRAINT records_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY roles ADD CONSTRAINT roles_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);
    ALTER TABLE ONLY users ADD CONSTRAINT users_pkey PRIMARY KEY (id);

    -- Add unique constraints
    ALTER TABLE ONLY brands ADD CONSTRAINT brands_name_key UNIQUE (name);
    ALTER TABLE ONLY roles ADD CONSTRAINT roles_name_key UNIQUE (name);
    ALTER TABLE ONLY users ADD CONSTRAINT users_email_key UNIQUE (email);
    ALTER TABLE ONLY users ADD CONSTRAINT users_username_key UNIQUE (username);

    -- Add foreign key constraints
    ALTER TABLE ONLY data_input_attribute_pending_requests ADD CONSTRAINT data_input_attribute_pending_requests_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
    ALTER TABLE ONLY data_input_attributes ADD CONSTRAINT data_input_attributes_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
    ALTER TABLE ONLY data_input_pending_requests ADD CONSTRAINT data_input_pending_requests_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
    ALTER TABLE ONLY data_inputs ADD CONSTRAINT data_inputs_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
    ALTER TABLE ONLY records ADD CONSTRAINT records_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
    ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
    ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
    ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES roles(id);
    ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);

  # Initialize basic data
  init_basic_data.sql: |
    -- Insert default brands
    INSERT INTO brands (id, name, description, created_by, updated_by) VALUES
    (1, 'Seven Retail', 'Seven Retail Group', 'system', 'system'),
    (2, 'Default Brand', 'Default brand for system', 'system', 'system');

    -- Insert roles
    INSERT INTO roles (id, name, created_by, updated_by) VALUES
    (1, 'admin', 'system', 'system'),
    (2, 'user', 'system', 'system'),
    (3, 'manager', 'system', 'system');

    -- Insert default admin user (password: admin123)
    INSERT INTO users (id, first_name, last_name, username, password, email, active, is_superuser, created_by, updated_by) VALUES
    (1, 'Admin', 'User', 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflHQrxK', '<EMAIL>', true, true, 'system', 'system');

    -- Insert test user (password: test123)
    INSERT INTO users (id, first_name, last_name, username, password, email, active, is_superuser, created_by, updated_by) VALUES
    (2, 'Test', 'User', 'testuser', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '<EMAIL>', true, false, 'system', 'system');

    -- Assign roles to users
    INSERT INTO user_roles (id, user_id, role_id, created_by, updated_by) VALUES
    (1, 1, 1, 'system', 'system'),  -- admin user gets admin role
    (2, 2, 2, 'system', 'system');  -- test user gets user role

    -- Assign brands to users
    INSERT INTO user_brands (id, user_id, brand_id, created_by, updated_by) VALUES
    (1, 1, 1, 'system', 'system'),  -- admin user gets Seven Retail brand
    (2, 1, 2, 'system', 'system'),  -- admin user gets Default brand
    (3, 2, 1, 'system', 'system');  -- test user gets Seven Retail brand

    -- Update sequences to start after inserted data
    SELECT setval('brands_id_seq', 2, true);
    SELECT setval('roles_id_seq', 3, true);
    SELECT setval('users_id_seq', 2, true);
    SELECT setval('user_roles_id_seq', 2, true);
    SELECT setval('user_brands_id_seq', 3, true);
