#!/bin/bash

# SR Internal Service - Redeploy Script
# This script cleans up the failed deployment and redeploys with the correct database structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="sr-internal-service"
PROJECT_ID="${PROJECT_ID:-}"
CLUSTER_NAME="${CLUSTER_NAME:-sr-internal-service-cluster}"
REGION="${REGION:-us-central1}"

# Helper functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
}

# Clean up existing deployment
cleanup_deployment() {
    print_status "Cleaning up existing deployment..."
    
    # Delete deployments first to stop pods
    kubectl delete deployment backend frontend postgresql -n $NAMESPACE --ignore-not-found=true
    
    # Wait for pods to terminate
    print_status "Waiting for pods to terminate..."
    kubectl wait --for=delete pod --all -n $NAMESPACE --timeout=120s || true
    
    # Delete other resources
    kubectl delete pvc postgresql-pvc -n $NAMESPACE --ignore-not-found=true
    
    print_success "Cleanup completed"
}

# Deploy resources
deploy_resources() {
    print_status "Deploying updated resources..."
    
    # Apply ConfigMaps first
    kubectl apply -f configmap.yaml
    kubectl apply -f secrets.yaml
    
    # Deploy PostgreSQL
    kubectl apply -f postgresql.yaml
    
    # Wait for PostgreSQL to be ready
    print_status "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgresql -n $NAMESPACE --timeout=300s
    
    # Deploy backend with database migration
    kubectl apply -f backend.yaml
    
    # Wait for backend to be ready (this includes the migration process)
    print_status "Waiting for backend deployment (including database migration)..."
    kubectl wait --for=condition=available deployment/backend -n $NAMESPACE --timeout=600s
    
    # Deploy frontend
    kubectl apply -f frontend.yaml
    
    # Wait for frontend to be ready
    print_status "Waiting for frontend deployment..."
    kubectl wait --for=condition=available deployment/frontend -n $NAMESPACE --timeout=300s
    
    # Apply remaining resources
    kubectl apply -f ingress.yaml
    kubectl apply -f hpa.yaml
    
    print_success "All resources deployed successfully"
}

# Get static IP and update configuration
update_configuration() {
    print_status "Getting static IP address..."
    
    # Get the static IP
    IP_ADDRESS=$(gcloud compute addresses describe sr-internal-service-ip --global --format="value(address)" 2>/dev/null || echo "")
    
    if [ ! -z "$IP_ADDRESS" ]; then
        print_status "Updating configuration with static IP: $IP_ADDRESS"
        
        # Update CORS origins
        kubectl patch configmap sr-internal-service-config -n $NAMESPACE --type merge -p "{\"data\":{\"BACKEND_CORS_ORIGINS\":\"[\\\"http://$IP_ADDRESS\\\", \\\"http://localhost:3000\\\"]\"}}"
        
        # Update frontend API base URL
        kubectl patch configmap sr-internal-service-config -n $NAMESPACE --type merge -p "{\"data\":{\"VITE_API_BASE_URL\":\"http://$IP_ADDRESS\"}}"
        
        # Restart deployments to pick up new config
        kubectl rollout restart deployment/frontend -n $NAMESPACE
        kubectl rollout restart deployment/backend -n $NAMESPACE
        
        print_success "Configuration updated successfully"
    else
        print_warning "Could not retrieve static IP address"
    fi
}

# Show deployment status
show_status() {
    print_status "Deployment Status:"
    echo
    
    print_status "Pods:"
    kubectl get pods -n $NAMESPACE
    echo
    
    print_status "Services:"
    kubectl get services -n $NAMESPACE
    echo
    
    print_status "Ingress:"
    kubectl get ingress -n $NAMESPACE
    echo
    
    # Show migration logs
    print_status "Database Migration Logs:"
    kubectl logs deployment/backend -c db-migration -n $NAMESPACE --tail=20 || echo "Migration logs not available yet"
    echo
    
    if [ ! -z "$IP_ADDRESS" ]; then
        print_success "🎉 Your application is available at:"
        echo "  Frontend: http://$IP_ADDRESS"
        echo "  Backend API: http://$IP_ADDRESS/api/v1"
        echo "  API Documentation: http://$IP_ADDRESS/docs"
        echo "  Alternative API Docs: http://$IP_ADDRESS/redoc"
        echo
        print_status "Static IP Address: $IP_ADDRESS"
    fi
}

# Main execution
main() {
    print_status "Starting redeployment process..."
    
    check_kubectl
    cleanup_deployment
    deploy_resources
    update_configuration
    show_status
    
    print_success "Redeployment completed successfully!"
    print_warning "Next steps:"
    echo "  1. Test your application at the provided URL"
    echo "  2. Check database structure: kubectl exec -it deployment/backend -n $NAMESPACE -- python -c \"from app.database import engine; print('Database connected successfully')\""
    echo "  3. Monitor logs: kubectl logs -f deployment/backend -n $NAMESPACE"
}

# Run main function
main "$@"
