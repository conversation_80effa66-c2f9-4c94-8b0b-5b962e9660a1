apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: sr-internal-service
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      initContainers:
      - name: db-migration
        image: postgres:17-alpine
        env:
        - name: PGPASSWORD
          value: "sr_password"
        command:
        - sh
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h postgresql-service -p 5432 -U sr_user; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          echo "PostgreSQL is ready!"

          echo "Checking if database is already initialized..."
          TABLE_COUNT=$(psql -h postgresql-service -U sr_user -d sr_internal_service -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ' || echo "0")

          if [ "$TABLE_COUNT" -gt "1" ]; then
            echo "Database already initialized with $TABLE_COUNT tables"
            exit 0
          fi

          echo "Initializing database structure..."

          # Create the complete database structure in one go
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Create data_type_enum
          CREATE TYPE data_type_enum AS ENUM (
              'Text', 'Number', 'Decimal', 'Boolean', 'Date', 'Datetime',
              'File', 'Image', 'Video', 'Audio', 'URL', 'Email'
          );

          -- Create audit log function
          CREATE OR REPLACE FUNCTION log_changes() RETURNS trigger
              LANGUAGE plpgsql AS $$
          DECLARE
              v_old_data jsonb;
              v_new_data jsonb;
          BEGIN
              IF (TG_OP = 'UPDATE') THEN
                  v_old_data = to_jsonb(OLD);
                  v_new_data = to_jsonb(NEW);
                  INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, new_data, query)
                  VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'U', v_old_data, v_new_data, current_query());
                  RETURN NEW;
              ELSIF (TG_OP = 'DELETE') THEN
                  v_old_data = to_jsonb(OLD);
                  INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, query)
                  VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'D', v_old_data, current_query());
                  RETURN OLD;
              ELSIF (TG_OP = 'INSERT') THEN
                  v_new_data = to_jsonb(NEW);
                  INSERT INTO audit_log (schema_name, table_name, user_name, action, new_data, query)
                  VALUES (TG_TABLE_SCHEMA::text, TG_TABLE_NAME::text, session_user::text, 'I', v_new_data, current_query());
                  RETURN NEW;
              END IF;
              RETURN NULL;
          END; $$;
          EOF

          echo "Creating tables..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Create all tables
          CREATE TABLE audit_log (
              id bigserial PRIMARY KEY,
              schema_name text NOT NULL,
              table_name text NOT NULL,
              user_name text,
              action_timestamp timestamp with time zone DEFAULT now() NOT NULL,
              action text NOT NULL,
              original_data jsonb,
              new_data jsonb,
              query text,
              CONSTRAINT audit_log_action_check CHECK ((action = ANY (ARRAY['I'::text, 'D'::text, 'U'::text])))
          );

          CREATE TABLE brands (
              id serial PRIMARY KEY,
              name character varying(255) NOT NULL UNIQUE,
              description character varying(255),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE roles (
              id serial PRIMARY KEY,
              name character varying(64) NOT NULL UNIQUE,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE users (
              id serial PRIMARY KEY,
              first_name character varying(64) NOT NULL,
              last_name character varying(64) NOT NULL,
              username character varying(64) NOT NULL UNIQUE,
              password character varying(256),
              email character varying(256) NOT NULL UNIQUE,
              active boolean DEFAULT true,
              is_superuser boolean DEFAULT false,
              last_login timestamp with time zone,
              login_count integer DEFAULT 0,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE data_inputs (
              id serial PRIMARY KEY,
              name character varying(100) NOT NULL,
              brand_id integer NOT NULL REFERENCES brands(id),
              description character varying(255),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE data_input_attributes (
              id serial PRIMARY KEY,
              data_input_id integer NOT NULL REFERENCES data_inputs(id),
              name character varying(100) NOT NULL,
              description character varying(255),
              data_type data_type_enum DEFAULT 'Text'::data_type_enum NOT NULL,
              is_required boolean DEFAULT false,
              is_unique boolean DEFAULT false,
              is_active boolean DEFAULT true,
              version integer DEFAULT 1,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255),
              format character varying(255),
              value_options jsonb
          );

          CREATE TABLE records (
              id serial PRIMARY KEY,
              data_input_id integer NOT NULL REFERENCES data_inputs(id),
              value jsonb,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE user_brands (
              id serial PRIMARY KEY,
              user_id integer NOT NULL REFERENCES users(id),
              brand_id integer NOT NULL REFERENCES brands(id),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE user_roles (
              id serial PRIMARY KEY,
              user_id integer NOT NULL REFERENCES users(id),
              role_id integer NOT NULL REFERENCES roles(id),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );
          EOF

          echo "Inserting initial data..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Insert initial data
          INSERT INTO brands (id, name, description, created_by, updated_by) VALUES
          (1, 'Seven Retail', 'Seven Retail Group', 'system', 'system'),
          (2, 'Default Brand', 'Default brand for system', 'system', 'system');

          INSERT INTO roles (id, name, created_by, updated_by) VALUES
          (1, 'admin', 'system', 'system'),
          (2, 'user', 'system', 'system'),
          (3, 'manager', 'system', 'system');

          -- Insert admin user (password: admin123 - hashed with bcrypt)
          INSERT INTO users (id, first_name, last_name, username, password, email, active, is_superuser, created_by, updated_by) VALUES
          (1, 'Admin', 'User', 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJflHQrxK', '<EMAIL>', true, true, 'system', 'system');

          -- Insert test user (password: test123 - hashed with bcrypt)
          INSERT INTO users (id, first_name, last_name, username, password, email, active, is_superuser, created_by, updated_by) VALUES
          (2, 'Test', 'User', 'testuser', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '<EMAIL>', true, false, 'system', 'system');

          -- Assign roles to users
          INSERT INTO user_roles (user_id, role_id, created_by, updated_by) VALUES
          (1, 1, 'system', 'system'),  -- admin user gets admin role
          (2, 2, 'system', 'system');  -- test user gets user role

          -- Assign brands to users
          INSERT INTO user_brands (user_id, brand_id, created_by, updated_by) VALUES
          (1, 1, 'system', 'system'),  -- admin user gets Seven Retail brand
          (1, 2, 'system', 'system'),  -- admin user gets Default brand
          (2, 1, 'system', 'system');  -- test user gets Seven Retail brand

          -- Update sequences
          SELECT setval('brands_id_seq', 2, true);
          SELECT setval('roles_id_seq', 3, true);
          SELECT setval('users_id_seq', 2, true);
          EOF

          echo "Database initialization completed successfully!"
      containers:
      - name: backend
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        ports:
        - containerPort: 8000
        env:
        # Database Configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: DATABASE_URL
        # Security
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: SECRET_KEY
        - name: ALGORITHM
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: ALGORITHM
        - name: ACCESS_TOKEN_EXPIRE_MINUTES
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: ACCESS_TOKEN_EXPIRE_MINUTES
        # API Configuration
        - name: API_V1_STR
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: API_V1_STR
        - name: PROJECT_NAME
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: PROJECT_NAME
        - name: PROJECT_VERSION
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: PROJECT_VERSION
        # CORS
        - name: BACKEND_CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: BACKEND_CORS_ORIGINS
        # File Storage
        - name: UPLOAD_DIR
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: UPLOAD_DIR
        - name: MAX_FILE_SIZE
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_FILE_SIZE
        - name: STORAGE_BACKEND
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: STORAGE_BACKEND
        # Image Processing
        - name: MAX_IMAGE_WIDTH
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_IMAGE_WIDTH
        - name: MAX_IMAGE_HEIGHT
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_IMAGE_HEIGHT
        - name: IMAGE_QUALITY
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: IMAGE_QUALITY
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: uploads-storage
          mountPath: /app/uploads
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: uploads-storage
        emptyDir: {}
      imagePullSecrets:
      - name: ghcr-secret

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sr-internal-service
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
