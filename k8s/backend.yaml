apiVersion: v1
kind: ConfigMap
metadata:
  name: init-db-script
  namespace: sr-internal-service
data:
  init_db.py: |
    #!/usr/bin/env python3
    import sys
    sys.path.append('/app')
    from sqlalchemy.orm import Session
    from app.core.database import SessionLocal, engine
    from app.core.security import get_password_hash
    from app.models import User, Role, Brand
    from app.core.database import Base

    print('Creating database schema...')
    Base.metadata.create_all(bind=engine)
    print('✓ Database schema created')

    db = SessionLocal()
    try:
        # Create roles
        admin_role = db.query(Role).filter(Role.name == 'admin').first()
        if not admin_role:
            admin_role = Role(name='admin', created_by='system', updated_by='system')
            db.add(admin_role)
        
        user_role = db.query(Role).filter(Role.name == 'user').first()
        if not user_role:
            user_role = Role(name='user', created_by='system', updated_by='system')
            db.add(user_role)
        
        db.commit()
        print('✓ Roles created')
        
        # Create admin user
        admin_user = db.query(User).filter(User.username == 'admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                password=get_password_hash('admin123'),
                is_superuser=True,
                active=True
            )
            db.add(admin_user)
            db.commit()
            print('✓ Admin user created (username: admin, password: admin123)')
        else:
            print('✓ Admin user already exists')
        
        print('🎉 Database initialization completed!')
    except Exception as e:
        print(f'❌ Error: {e}')
        db.rollback()
    finally:
        db.close()

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: sr-internal-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      initContainers:
      - name: db-migration
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:latest
        imagePullPolicy: Always
        env:
        - name: DATABASE_URL
          value: "********************************************************/sr_internal_service"
        - name: PGPASSWORD
          value: "sr_password"
        volumeMounts:
        - name: init-script
          mountPath: /scripts
        command:
        - sh
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until python -c "import psycopg2; psycopg2.connect(host='postgresql-service', port=5432, user='sr_user', password='sr_password', database='sr_internal_service').close()"; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          echo "PostgreSQL is ready!"

          echo "Initializing database schema and creating admin users..."
          cd /app
          python /scripts/init_db.py
          
          echo "Database migration completed successfully!"
      containers:
      - name: backend
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "********************************************************/sr_internal_service"
        - name: SECRET_KEY
          value: "your-secret-key-here"
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: init-script
        configMap:
          name: init-db-script

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sr-internal-service
spec:
  selector:
    app: backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
