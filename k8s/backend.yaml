apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: sr-internal-service
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      initContainers:
      - name: db-migration
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: DATABASE_URL
        command:
        - sh
        - -c
        - |
          echo "Installing PostgreSQL client..."
          apk add --no-cache postgresql-client

          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h postgresql-service -p 5432 -U sr_user; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          echo "PostgreSQL is ready!"

          echo "Running SQL migration scripts..."
          cd /app

          # Run SQL migrations if they exist
          if [ -d "/app/migrations" ]; then
            for sql_file in /app/migrations/*.sql; do
              if [ -f "$sql_file" ]; then
                echo "Running migration: $(basename $sql_file)"
                PGPASSWORD=sr_password psql -h postgresql-service -U sr_user -d sr_internal_service -f "$sql_file" || echo "Migration $(basename $sql_file) completed with warnings"
              fi
            done
          fi

          echo "Running alembic migrations..."
          alembic upgrade head || echo "No alembic migrations found, continuing..."

          echo "Running initialization script..."
          python scripts/init_db.py || echo "Init script completed with warnings"

          echo "Database setup completed!"
        volumeMounts:
        - name: migration-scripts
          mountPath: /app/migrations
          readOnly: true
      containers:
      - name: backend
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        ports:
        - containerPort: 8000
        env:
        # Database Configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: DATABASE_URL
        # Security
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: SECRET_KEY
        - name: ALGORITHM
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: ALGORITHM
        - name: ACCESS_TOKEN_EXPIRE_MINUTES
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: ACCESS_TOKEN_EXPIRE_MINUTES
        # API Configuration
        - name: API_V1_STR
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: API_V1_STR
        - name: PROJECT_NAME
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: PROJECT_NAME
        - name: PROJECT_VERSION
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: PROJECT_VERSION
        # CORS
        - name: BACKEND_CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: BACKEND_CORS_ORIGINS
        # File Storage
        - name: UPLOAD_DIR
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: UPLOAD_DIR
        - name: MAX_FILE_SIZE
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_FILE_SIZE
        - name: STORAGE_BACKEND
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: STORAGE_BACKEND
        # Image Processing
        - name: MAX_IMAGE_WIDTH
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_IMAGE_WIDTH
        - name: MAX_IMAGE_HEIGHT
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_IMAGE_HEIGHT
        - name: IMAGE_QUALITY
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: IMAGE_QUALITY
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: uploads-storage
          mountPath: /app/uploads
      volumes:
      - name: uploads-storage
        emptyDir: {}
      - name: migration-scripts
        configMap:
          name: migration-scripts
      imagePullSecrets:
      - name: ghcr-secret

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sr-internal-service
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
