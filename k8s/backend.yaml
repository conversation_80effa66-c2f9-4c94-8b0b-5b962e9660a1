apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: sr-internal-service
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      initContainers:
      - name: db-migration
        image: postgres:17-alpine
        env:
        - name: PGPASSWORD
          value: "sr_password"
        command:
        - sh
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h postgresql-service -p 5432 -U sr_user; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          echo "PostgreSQL is ready!"

          echo "Checking if database is already initialized..."
          TABLE_COUNT=$(psql -h postgresql-service -U sr_user -d sr_internal_service -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ' || echo "0")

          if [ "$TABLE_COUNT" -gt "5" ]; then
            echo "Database already initialized with $TABLE_COUNT tables"
            exit 0
          fi

          echo "Starting complete database migration with all 30 tables and data..."

          # Create all enum types first
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Create all enum types
          CREATE TYPE audit_action AS ENUM (
              'create', 'read', 'update', 'delete', 'login', 'logout', 'login_failed',
              'password_change', 'password_reset', 'access_granted', 'access_denied',
              'permission_change', 'role_change', 'data_export', 'data_import',
              'file_upload', 'file_download', 'file_delete', 'system_backup',
              'system_restore', 'configuration_change'
          );
          CREATE TYPE audit_severity AS ENUM ('low', 'medium', 'high', 'critical');
          CREATE TYPE brand_role AS ENUM ('viewer', 'editor', 'admin', 'owner');
          CREATE TYPE data_type_enum AS ENUM (
              'Text', 'Number', 'Decimal', 'Boolean', 'Date', 'Datetime',
              'File', 'Image', 'Video', 'Audio', 'URL', 'Email'
          );
          CREATE TYPE datatypeenum AS ENUM (
              'TEXT', 'NUMBER', 'DECIMAL', 'BOOLEAN', 'DATE', 'DATETIME',
              'FILE', 'IMAGE', 'VIDEO', 'AUDIO', 'URL', 'EMAIL'
          );
          CREATE TYPE delivery_method AS ENUM ('download', 'email', 'ftp', 's3');
          CREATE TYPE export_format AS ENUM ('csv', 'xlsx', 'json', 'pdf');
          CREATE TYPE export_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'cancelled');
          CREATE TYPE export_type AS ENUM ('records', 'statistics', 'template');
          CREATE TYPE file_status AS ENUM ('uploading', 'processing', 'ready', 'error', 'quarantined', 'deleted');
          CREATE TYPE file_type AS ENUM ('image', 'document', 'audio', 'video', 'archive', 'other');
          CREATE TYPE request_priority AS ENUM ('low', 'medium', 'high', 'urgent');
          CREATE TYPE request_status AS ENUM ('pending', 'under_review', 'approved', 'rejected', 'cancelled', 'implemented');
          CREATE TYPE request_type AS ENUM (
              'create_data_input', 'update_data_input', 'delete_data_input',
              'create_attribute', 'update_attribute', 'delete_attribute',
              'create_brand', 'update_brand', 'delete_brand'
          );
          CREATE TYPE schedule_type AS ENUM ('daily', 'weekly', 'monthly', 'custom');
          CREATE TYPE storage_backend AS ENUM ('local', 's3', 'gcs');
          EOF

          echo "Creating functions..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Create functions
          CREATE OR REPLACE FUNCTION check_brand_permission(user_id_param integer, brand_id_param integer, required_role character varying) RETURNS boolean
              LANGUAGE plpgsql
              AS $$
          BEGIN
              RETURN EXISTS (
                  SELECT 1
                  FROM user_brands ub
                  WHERE ub.user_id = user_id_param
                    AND ub.brand_id = brand_id_param
              );
          END;
          $$;

          CREATE OR REPLACE FUNCTION log_changes() RETURNS trigger
              LANGUAGE plpgsql
              AS $$
          DECLARE
              v_old_data jsonb;
              v_new_data jsonb;
          BEGIN
              IF (TG_OP = 'UPDATE') THEN
                  v_old_data := to_jsonb(OLD);
                  v_new_data := to_jsonb(NEW);
                  INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, new_data, query)
                  VALUES (TG_TABLE_SCHEMA::TEXT, TG_TABLE_NAME::TEXT, session_user::TEXT, 'U', v_old_data, v_new_data, current_query());
                  RETURN NEW;
              ELSIF (TG_OP = 'DELETE') THEN
                  v_old_data := to_jsonb(OLD);
                  INSERT INTO audit_log (schema_name, table_name, user_name, action, original_data, query)
                  VALUES (TG_TABLE_SCHEMA::TEXT, TG_TABLE_NAME::TEXT, session_user::TEXT, 'D', v_old_data, current_query());
                  RETURN OLD;
              ELSIF (TG_OP = 'INSERT') THEN
                  v_new_data := to_jsonb(NEW);
                  INSERT INTO audit_log (schema_name, table_name, user_name, action, new_data, query)
                  VALUES (TG_TABLE_SCHEMA::TEXT, TG_TABLE_NAME::TEXT, session_user::TEXT, 'I', v_new_data, current_query());
                  RETURN NEW;
              END IF;
              RETURN NULL;
          END;
          $$;

          CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS trigger
              LANGUAGE plpgsql
              AS $$
          BEGIN
              NEW.updated_at = now();
              RETURN NEW;
          END;
          $$;

          CREATE OR REPLACE FUNCTION update_user_timestamps() RETURNS trigger
              LANGUAGE plpgsql
              AS $$
          BEGIN
              NEW.changed_on = now();
              RETURN NEW;
          END;
          $$;
          EOF

          echo "Creating all 30 tables..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Create all 30 tables from restore.sql
          CREATE TABLE alembic_version (
              version_num character varying(32) NOT NULL
          );

          CREATE TABLE audit_log (
              id bigint NOT NULL,
              schema_name text NOT NULL,
              table_name text NOT NULL,
              user_name text,
              action_timestamp timestamp with time zone DEFAULT now() NOT NULL,
              action character(1) NOT NULL,
              original_data jsonb,
              new_data jsonb,
              query text
          );

          CREATE TABLE brand_access_audit (
              id integer NOT NULL,
              user_id integer NOT NULL,
              brand_id integer NOT NULL,
              action audit_action NOT NULL,
              timestamp timestamp with time zone DEFAULT now() NOT NULL,
              ip_address inet,
              user_agent text,
              details jsonb
          );

          CREATE TABLE brands (
              id integer NOT NULL,
              name character varying(255) NOT NULL,
              description character varying(255),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE data_exports (
              id integer NOT NULL,
              name character varying(255) NOT NULL,
              description text,
              data_input_id integer,
              export_type export_type NOT NULL,
              format export_format NOT NULL,
              filters jsonb,
              columns jsonb,
              status export_status DEFAULT 'pending'::export_status NOT NULL,
              file_path character varying(500),
              file_size bigint,
              row_count integer,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255),
              completed_at timestamp with time zone,
              error_message text,
              brand_id integer
          );

          CREATE TABLE data_input_attribute_pending_requests (
              id integer NOT NULL,
              data_input_id integer,
              name character varying(100) NOT NULL,
              description character varying(255),
              request_status request_status NOT NULL,
              request_message text,
              data_type data_type_enum DEFAULT 'Text'::data_type_enum NOT NULL,
              is_required boolean DEFAULT false NOT NULL,
              value_options jsonb,
              format character varying(100),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE data_input_attributes (
              id integer NOT NULL,
              data_input_id integer NOT NULL,
              name character varying(100) NOT NULL,
              description character varying(255),
              data_type data_type_enum DEFAULT 'Text'::data_type_enum NOT NULL,
              is_required boolean DEFAULT false NOT NULL,
              value_options jsonb,
              format character varying(100),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE data_input_audit_logs (
              id integer NOT NULL,
              data_input_id integer NOT NULL,
              action audit_action NOT NULL,
              old_values jsonb,
              new_values jsonb,
              changed_by character varying(255),
              timestamp timestamp with time zone DEFAULT now() NOT NULL,
              ip_address inet,
              user_agent text
          );

          CREATE TABLE data_input_pending_requests (
              id integer NOT NULL,
              brand_id integer NOT NULL,
              name character varying(100) NOT NULL,
              description character varying(255),
              request_status request_status NOT NULL,
              request_message text,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE data_inputs (
              id integer NOT NULL,
              name character varying(100) NOT NULL,
              brand_id integer NOT NULL,
              description character varying(255),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE export_access_logs (
              id integer NOT NULL,
              export_id integer NOT NULL,
              user_id integer NOT NULL,
              action audit_action NOT NULL,
              timestamp timestamp with time zone DEFAULT now() NOT NULL,
              ip_address inet,
              user_agent text,
              details jsonb
          );

          CREATE TABLE export_schedules (
              id integer NOT NULL,
              export_id integer NOT NULL,
              schedule_type schedule_type NOT NULL,
              schedule_config jsonb NOT NULL,
              is_active boolean DEFAULT true NOT NULL,
              next_run timestamp with time zone,
              last_run timestamp with time zone,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE export_shares (
              id integer NOT NULL,
              export_id integer NOT NULL,
              shared_with_user_id integer,
              shared_with_email character varying(320),
              permissions jsonb DEFAULT '{"read": true, "download": true}'::jsonb NOT NULL,
              expires_at timestamp with time zone,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255)
          );

          CREATE TABLE export_templates (
              id integer NOT NULL,
              name character varying(255) NOT NULL,
              description text,
              data_input_id integer,
              template_config jsonb NOT NULL,
              is_public boolean DEFAULT false NOT NULL,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255),
              brand_id integer
          );

          CREATE TABLE file_access_logs (
              id integer NOT NULL,
              file_id integer NOT NULL,
              user_id integer NOT NULL,
              action audit_action NOT NULL,
              timestamp timestamp with time zone DEFAULT now() NOT NULL,
              ip_address inet,
              user_agent text,
              details jsonb
          );

          CREATE TABLE file_processing_jobs (
              id integer NOT NULL,
              file_id integer NOT NULL,
              job_type character varying(100) NOT NULL,
              status character varying(50) DEFAULT 'pending'::character varying NOT NULL,
              progress integer DEFAULT 0,
              result jsonb,
              error_message text,
              started_at timestamp with time zone,
              completed_at timestamp with time zone,
              created_at timestamp with time zone DEFAULT now() NOT NULL
          );

          CREATE TABLE file_records (
              id integer NOT NULL,
              file_id integer NOT NULL,
              record_id integer NOT NULL,
              created_at timestamp with time zone DEFAULT now() NOT NULL
          );

          CREATE TABLE file_shares (
              id integer NOT NULL,
              file_id integer NOT NULL,
              shared_with_user_id integer,
              shared_with_email character varying(320),
              permissions jsonb DEFAULT '{"read": true, "download": true}'::jsonb NOT NULL,
              expires_at timestamp with time zone,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255)
          );

          CREATE TABLE file_versions (
              id integer NOT NULL,
              file_id integer NOT NULL,
              version_number integer NOT NULL,
              file_path character varying(500) NOT NULL,
              file_size bigint NOT NULL,
              checksum character varying(64),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255)
          );

          CREATE TABLE files (
              id integer NOT NULL,
              original_filename character varying(255) NOT NULL,
              stored_filename character varying(255) NOT NULL,
              file_path character varying(500) NOT NULL,
              file_size bigint NOT NULL,
              mime_type character varying(100),
              file_type file_type,
              status file_status DEFAULT 'uploading'::file_status NOT NULL,
              storage_backend storage_backend DEFAULT 'local'::storage_backend NOT NULL,
              metadata jsonb,
              checksum character varying(64),
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255),
              brand_id integer
          );

          CREATE TABLE records (
              id integer NOT NULL,
              data_input_id integer NOT NULL,
              value jsonb,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE roles (
              id integer NOT NULL,
              name character varying(64) NOT NULL,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE security_audit_logs (
              id integer NOT NULL,
              event_type character varying(100) NOT NULL,
              severity audit_severity NOT NULL,
              user_id integer,
              ip_address inet,
              user_agent text,
              details jsonb NOT NULL,
              timestamp timestamp with time zone DEFAULT now() NOT NULL
          );

          CREATE TABLE system_audit_logs (
              id integer NOT NULL,
              event_type character varying(100) NOT NULL,
              severity audit_severity NOT NULL,
              component character varying(100),
              details jsonb NOT NULL,
              timestamp timestamp with time zone DEFAULT now() NOT NULL
          );

          CREATE TABLE user_audit_logs (
              id integer NOT NULL,
              user_id integer NOT NULL,
              action audit_action NOT NULL,
              old_values jsonb,
              new_values jsonb,
              changed_by character varying(255),
              timestamp timestamp with time zone DEFAULT now() NOT NULL,
              ip_address inet,
              user_agent text
          );

          CREATE TABLE user_brands (
              id integer NOT NULL,
              user_id integer NOT NULL,
              brand_id integer NOT NULL,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE user_roles (
              id integer NOT NULL,
              user_id integer NOT NULL,
              role_id integer NOT NULL,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE users (
              id integer NOT NULL,
              first_name character varying(64) NOT NULL,
              last_name character varying(64) NOT NULL,
              username character varying(64) NOT NULL,
              password character varying(256),
              email character varying(320) NOT NULL,
              active boolean DEFAULT true NOT NULL,
              is_superuser boolean DEFAULT false NOT NULL,
              login_count integer,
              fail_login_count integer,
              last_login timestamp with time zone,
              created_on timestamp with time zone DEFAULT now() NOT NULL,
              changed_on timestamp with time zone DEFAULT now() NOT NULL,
              created_by_fk integer,
              changed_by_fk integer,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              created_by character varying(255),
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_by character varying(255)
          );

          CREATE TABLE workflow_audit_logs (
              id integer NOT NULL,
              request_id integer NOT NULL,
              action audit_action NOT NULL,
              old_values jsonb,
              new_values jsonb,
              changed_by character varying(255),
              timestamp timestamp with time zone DEFAULT now() NOT NULL,
              ip_address inet,
              user_agent text
          );

          CREATE TABLE workflow_requests (
              id integer NOT NULL,
              request_type request_type NOT NULL,
              title character varying(255) NOT NULL,
              description text,
              priority request_priority DEFAULT 'medium'::request_priority NOT NULL,
              status request_status DEFAULT 'pending'::request_status NOT NULL,
              requested_by character varying(255) NOT NULL,
              assigned_to character varying(255),
              brand_id integer,
              data_input_id integer,
              request_data jsonb,
              response_data jsonb,
              created_at timestamp with time zone DEFAULT now() NOT NULL,
              updated_at timestamp with time zone DEFAULT now() NOT NULL,
              resolved_at timestamp with time zone
          );
          EOF

          echo "Creating primary keys and constraints..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Add primary keys and constraints
          ALTER TABLE ONLY alembic_version ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);
          ALTER TABLE ONLY audit_log ADD CONSTRAINT audit_log_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY brand_access_audit ADD CONSTRAINT brand_access_audit_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY brands ADD CONSTRAINT brands_name_key UNIQUE (name);
          ALTER TABLE ONLY brands ADD CONSTRAINT brands_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY data_exports ADD CONSTRAINT data_exports_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY data_input_attribute_pending_requests ADD CONSTRAINT data_input_attribute_pending_requests_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY data_input_attributes ADD CONSTRAINT data_input_attributes_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY data_input_audit_logs ADD CONSTRAINT data_input_audit_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY data_input_pending_requests ADD CONSTRAINT data_input_pending_requests_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY data_inputs ADD CONSTRAINT data_inputs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY export_access_logs ADD CONSTRAINT export_access_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY export_schedules ADD CONSTRAINT export_schedules_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY export_shares ADD CONSTRAINT export_shares_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY export_templates ADD CONSTRAINT export_templates_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY file_access_logs ADD CONSTRAINT file_access_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY file_processing_jobs ADD CONSTRAINT file_processing_jobs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY file_records ADD CONSTRAINT file_records_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY file_shares ADD CONSTRAINT file_shares_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY file_versions ADD CONSTRAINT file_versions_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY files ADD CONSTRAINT files_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY records ADD CONSTRAINT records_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY roles ADD CONSTRAINT roles_name_key UNIQUE (name);
          ALTER TABLE ONLY roles ADD CONSTRAINT roles_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY security_audit_logs ADD CONSTRAINT security_audit_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY system_audit_logs ADD CONSTRAINT system_audit_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY user_audit_logs ADD CONSTRAINT user_audit_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_user_id_brand_id_key UNIQUE (user_id, brand_id);
          ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_user_id_role_id_key UNIQUE (user_id, role_id);
          ALTER TABLE ONLY users ADD CONSTRAINT users_email_key UNIQUE (email);
          ALTER TABLE ONLY users ADD CONSTRAINT users_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY users ADD CONSTRAINT users_username_key UNIQUE (username);
          ALTER TABLE ONLY workflow_audit_logs ADD CONSTRAINT workflow_audit_logs_pkey PRIMARY KEY (id);
          ALTER TABLE ONLY workflow_requests ADD CONSTRAINT workflow_requests_pkey PRIMARY KEY (id);

          -- Create sequences
          CREATE SEQUENCE audit_log_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE brand_access_audit_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE brands_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE data_exports_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE data_input_attribute_pending_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE data_input_attributes_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE data_input_audit_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE data_input_pending_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE data_inputs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE export_access_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE export_schedules_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE export_shares_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE export_templates_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE file_access_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE file_processing_jobs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE file_records_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE file_shares_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE file_versions_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE files_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE records_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE roles_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE security_audit_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE system_audit_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE user_audit_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE user_brands_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE user_roles_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE users_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE workflow_audit_logs_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;
          CREATE SEQUENCE workflow_requests_id_seq START WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE 1;

          -- Set sequence ownership
          ALTER SEQUENCE audit_log_id_seq OWNED BY audit_log.id;
          ALTER SEQUENCE brand_access_audit_id_seq OWNED BY brand_access_audit.id;
          ALTER SEQUENCE brands_id_seq OWNED BY brands.id;
          ALTER SEQUENCE data_exports_id_seq OWNED BY data_exports.id;
          ALTER SEQUENCE data_input_attribute_pending_requests_id_seq OWNED BY data_input_attribute_pending_requests.id;
          ALTER SEQUENCE data_input_attributes_id_seq OWNED BY data_input_attributes.id;
          ALTER SEQUENCE data_input_audit_logs_id_seq OWNED BY data_input_audit_logs.id;
          ALTER SEQUENCE data_input_pending_requests_id_seq OWNED BY data_input_pending_requests.id;
          ALTER SEQUENCE data_inputs_id_seq OWNED BY data_inputs.id;
          ALTER SEQUENCE export_access_logs_id_seq OWNED BY export_access_logs.id;
          ALTER SEQUENCE export_schedules_id_seq OWNED BY export_schedules.id;
          ALTER SEQUENCE export_shares_id_seq OWNED BY export_shares.id;
          ALTER SEQUENCE export_templates_id_seq OWNED BY export_templates.id;
          ALTER SEQUENCE file_access_logs_id_seq OWNED BY file_access_logs.id;
          ALTER SEQUENCE file_processing_jobs_id_seq OWNED BY file_processing_jobs.id;
          ALTER SEQUENCE file_records_id_seq OWNED BY file_records.id;
          ALTER SEQUENCE file_shares_id_seq OWNED BY file_shares.id;
          ALTER SEQUENCE file_versions_id_seq OWNED BY file_versions.id;
          ALTER SEQUENCE files_id_seq OWNED BY files.id;
          ALTER SEQUENCE records_id_seq OWNED BY records.id;
          ALTER SEQUENCE roles_id_seq OWNED BY roles.id;
          ALTER SEQUENCE security_audit_logs_id_seq OWNED BY security_audit_logs.id;
          ALTER SEQUENCE system_audit_logs_id_seq OWNED BY system_audit_logs.id;
          ALTER SEQUENCE user_audit_logs_id_seq OWNED BY user_audit_logs.id;
          ALTER SEQUENCE user_brands_id_seq OWNED BY user_brands.id;
          ALTER SEQUENCE user_roles_id_seq OWNED BY user_roles.id;
          ALTER SEQUENCE users_id_seq OWNED BY users.id;
          ALTER SEQUENCE workflow_audit_logs_id_seq OWNED BY workflow_audit_logs.id;
          ALTER SEQUENCE workflow_requests_id_seq OWNED BY workflow_requests.id;

          -- Set default values for sequences
          ALTER TABLE ONLY audit_log ALTER COLUMN id SET DEFAULT nextval('audit_log_id_seq'::regclass);
          ALTER TABLE ONLY brand_access_audit ALTER COLUMN id SET DEFAULT nextval('brand_access_audit_id_seq'::regclass);
          ALTER TABLE ONLY brands ALTER COLUMN id SET DEFAULT nextval('brands_id_seq'::regclass);
          ALTER TABLE ONLY data_exports ALTER COLUMN id SET DEFAULT nextval('data_exports_id_seq'::regclass);
          ALTER TABLE ONLY data_input_attribute_pending_requests ALTER COLUMN id SET DEFAULT nextval('data_input_attribute_pending_requests_id_seq'::regclass);
          ALTER TABLE ONLY data_input_attributes ALTER COLUMN id SET DEFAULT nextval('data_input_attributes_id_seq'::regclass);
          ALTER TABLE ONLY data_input_audit_logs ALTER COLUMN id SET DEFAULT nextval('data_input_audit_logs_id_seq'::regclass);
          ALTER TABLE ONLY data_input_pending_requests ALTER COLUMN id SET DEFAULT nextval('data_input_pending_requests_id_seq'::regclass);
          ALTER TABLE ONLY data_inputs ALTER COLUMN id SET DEFAULT nextval('data_inputs_id_seq'::regclass);
          ALTER TABLE ONLY export_access_logs ALTER COLUMN id SET DEFAULT nextval('export_access_logs_id_seq'::regclass);
          ALTER TABLE ONLY export_schedules ALTER COLUMN id SET DEFAULT nextval('export_schedules_id_seq'::regclass);
          ALTER TABLE ONLY export_shares ALTER COLUMN id SET DEFAULT nextval('export_shares_id_seq'::regclass);
          ALTER TABLE ONLY export_templates ALTER COLUMN id SET DEFAULT nextval('export_templates_id_seq'::regclass);
          ALTER TABLE ONLY file_access_logs ALTER COLUMN id SET DEFAULT nextval('file_access_logs_id_seq'::regclass);
          ALTER TABLE ONLY file_processing_jobs ALTER COLUMN id SET DEFAULT nextval('file_processing_jobs_id_seq'::regclass);
          ALTER TABLE ONLY file_records ALTER COLUMN id SET DEFAULT nextval('file_records_id_seq'::regclass);
          ALTER TABLE ONLY file_shares ALTER COLUMN id SET DEFAULT nextval('file_shares_id_seq'::regclass);
          ALTER TABLE ONLY file_versions ALTER COLUMN id SET DEFAULT nextval('file_versions_id_seq'::regclass);
          ALTER TABLE ONLY files ALTER COLUMN id SET DEFAULT nextval('files_id_seq'::regclass);
          ALTER TABLE ONLY records ALTER COLUMN id SET DEFAULT nextval('records_id_seq'::regclass);
          ALTER TABLE ONLY roles ALTER COLUMN id SET DEFAULT nextval('roles_id_seq'::regclass);
          ALTER TABLE ONLY security_audit_logs ALTER COLUMN id SET DEFAULT nextval('security_audit_logs_id_seq'::regclass);
          ALTER TABLE ONLY system_audit_logs ALTER COLUMN id SET DEFAULT nextval('system_audit_logs_id_seq'::regclass);
          ALTER TABLE ONLY user_audit_logs ALTER COLUMN id SET DEFAULT nextval('user_audit_logs_id_seq'::regclass);
          ALTER TABLE ONLY user_brands ALTER COLUMN id SET DEFAULT nextval('user_brands_id_seq'::regclass);
          ALTER TABLE ONLY user_roles ALTER COLUMN id SET DEFAULT nextval('user_roles_id_seq'::regclass);
          ALTER TABLE ONLY users ALTER COLUMN id SET DEFAULT nextval('users_id_seq'::regclass);
          ALTER TABLE ONLY workflow_audit_logs ALTER COLUMN id SET DEFAULT nextval('workflow_audit_logs_id_seq'::regclass);
          ALTER TABLE ONLY workflow_requests ALTER COLUMN id SET DEFAULT nextval('workflow_requests_id_seq'::regclass);
          EOF

          echo "Adding foreign key constraints..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'
          -- Add foreign key constraints
          ALTER TABLE ONLY brand_access_audit ADD CONSTRAINT brand_access_audit_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY brand_access_audit ADD CONSTRAINT brand_access_audit_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY data_exports ADD CONSTRAINT data_exports_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY data_exports ADD CONSTRAINT data_exports_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          ALTER TABLE ONLY data_input_attribute_pending_requests ADD CONSTRAINT data_input_attribute_pending_requests_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          ALTER TABLE ONLY data_input_attributes ADD CONSTRAINT data_input_attributes_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          ALTER TABLE ONLY data_input_audit_logs ADD CONSTRAINT data_input_audit_logs_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          ALTER TABLE ONLY data_input_pending_requests ADD CONSTRAINT data_input_pending_requests_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY data_inputs ADD CONSTRAINT data_inputs_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY export_access_logs ADD CONSTRAINT export_access_logs_export_id_fkey FOREIGN KEY (export_id) REFERENCES data_exports(id);
          ALTER TABLE ONLY export_access_logs ADD CONSTRAINT export_access_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY export_schedules ADD CONSTRAINT export_schedules_export_id_fkey FOREIGN KEY (export_id) REFERENCES data_exports(id);
          ALTER TABLE ONLY export_shares ADD CONSTRAINT export_shares_export_id_fkey FOREIGN KEY (export_id) REFERENCES data_exports(id);
          ALTER TABLE ONLY export_shares ADD CONSTRAINT export_shares_shared_with_user_id_fkey FOREIGN KEY (shared_with_user_id) REFERENCES users(id);
          ALTER TABLE ONLY export_templates ADD CONSTRAINT export_templates_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY export_templates ADD CONSTRAINT export_templates_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          ALTER TABLE ONLY file_access_logs ADD CONSTRAINT file_access_logs_file_id_fkey FOREIGN KEY (file_id) REFERENCES files(id);
          ALTER TABLE ONLY file_access_logs ADD CONSTRAINT file_access_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY file_processing_jobs ADD CONSTRAINT file_processing_jobs_file_id_fkey FOREIGN KEY (file_id) REFERENCES files(id);
          ALTER TABLE ONLY file_records ADD CONSTRAINT file_records_file_id_fkey FOREIGN KEY (file_id) REFERENCES files(id);
          ALTER TABLE ONLY file_records ADD CONSTRAINT file_records_record_id_fkey FOREIGN KEY (record_id) REFERENCES records(id);
          ALTER TABLE ONLY file_shares ADD CONSTRAINT file_shares_file_id_fkey FOREIGN KEY (file_id) REFERENCES files(id);
          ALTER TABLE ONLY file_shares ADD CONSTRAINT file_shares_shared_with_user_id_fkey FOREIGN KEY (shared_with_user_id) REFERENCES users(id);
          ALTER TABLE ONLY file_versions ADD CONSTRAINT file_versions_file_id_fkey FOREIGN KEY (file_id) REFERENCES files(id);
          ALTER TABLE ONLY files ADD CONSTRAINT files_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY records ADD CONSTRAINT records_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          ALTER TABLE ONLY security_audit_logs ADD CONSTRAINT security_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY user_audit_logs ADD CONSTRAINT user_audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY user_brands ADD CONSTRAINT user_brands_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES roles(id);
          ALTER TABLE ONLY user_roles ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
          ALTER TABLE ONLY users ADD CONSTRAINT users_changed_by_fk_fkey FOREIGN KEY (changed_by_fk) REFERENCES users(id);
          ALTER TABLE ONLY users ADD CONSTRAINT users_created_by_fk_fkey FOREIGN KEY (created_by_fk) REFERENCES users(id);
          ALTER TABLE ONLY workflow_audit_logs ADD CONSTRAINT workflow_audit_logs_request_id_fkey FOREIGN KEY (request_id) REFERENCES workflow_requests(id);
          ALTER TABLE ONLY workflow_requests ADD CONSTRAINT workflow_requests_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brands(id);
          ALTER TABLE ONLY workflow_requests ADD CONSTRAINT workflow_requests_data_input_id_fkey FOREIGN KEY (data_input_id) REFERENCES data_inputs(id);
          EOF

          echo "Inserting all data from .dat files..."
          psql -h postgresql-service -U sr_user -d sr_internal_service << 'EOF'

          -- Insert data from roles table (4370.dat)
          INSERT INTO public.roles VALUES (1, 'Super Admin', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (2, 'Brand Admin', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (3, 'Data Architect', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (4, 'Data Editor', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (5, 'Data Entry', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (6, 'Viewer', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');

          -- Insert data from brands table (4371.dat)
          INSERT INTO public.brands VALUES (1, 'Sozo Skin', 'Brand vertical for skincare products and services.', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.brands VALUES (2, 'Sozo Dental', 'Brand vertical for dental care clinics and products.', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.brands VALUES (3, 'Sparks', 'Brand vertical for professional training and development.', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.brands VALUES (4, 'Golden Lamian', 'Brand vertical for the Golden Lamian food and beverage chain.', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');

          -- Insert data from users table (4372.dat)
          INSERT INTO public.users VALUES (7, 'Super', 'test', 'superadmin', '$2b$12$1TGr/XOZULdXYa8ScybLf.KAD0X4hfz/dFYW1csmt0kHKQDXqZOvm', '<EMAIL>', true, true, NULL, 0, 0, '2025-06-12 22:20:54.532129+07', '2025-06-13 21:05:07.954843+07', NULL, NULL, '2025-06-12 22:20:54.532129+07', 'system', '2025-06-13 21:05:07.954843+07', 'superadmin');
          INSERT INTO public.users VALUES (9, 'Ilyas', 'Perlindungan', 'ipman', '$2b$12$0fAs/wct.pzBjJPKUloF9OQMwAjMc6SHVeZOaRKS.BziQrkwhh8m.', '<EMAIL>', true, true, NULL, 0, 0, '2025-06-13 21:06:54.098089+07', '2025-06-13 21:08:31.624605+07', NULL, NULL, '2025-06-13 21:06:54.098089+07', 'superadmin', '2025-06-13 21:08:31.624605+07', 'superadmin');
          INSERT INTO public.users VALUES (13, 'Sonya', 'Mazayanti', 'sonyam', '$2b$12$O5jB7vJzbx0I9pkpIkPFFeDs8v1WueRhcl/MmtgvvtnBkohBIRCFW', '<EMAIL>', true, false, NULL, 0, 0, '2025-06-14 16:20:08.578701+07', '2025-06-14 16:20:08.578701+07', 9, 9, '2025-06-14 16:20:08.578701+07', NULL, '2025-06-14 16:20:08.578701+07', NULL);
          INSERT INTO public.users VALUES (5, 'Raymond', '-', 'raymond', 'placeholder_hash', '<EMAIL>', false, false, NULL, NULL, NULL, '2025-06-12 16:54:43.906484+07', '2025-06-14 16:21:18.199488+07', NULL, 9, '2025-06-12 16:54:43.906484+07', 'system_setup', '2025-06-14 16:21:18.199488+07', 'system_setup');
          INSERT INTO public.users VALUES (1, 'Admin', 'User', 'admin', 'placeholder_hash', '<EMAIL>', false, true, NULL, NULL, NULL, '2025-06-12 16:54:43.906484+07', '2025-06-14 16:21:31.887327+07', NULL, 9, '2025-06-12 16:54:43.906484+07', 'system_setup', '2025-06-14 16:21:31.887327+07', 'system_setup');
          INSERT INTO public.users VALUES (4, 'Raihan', 'Dimas P', 'raihandimas', 'placeholder_hash', '<EMAIL>', false, false, NULL, NULL, NULL, '2025-06-12 16:54:43.906484+07', '2025-06-14 16:22:05.673311+07', NULL, 9, '2025-06-12 16:54:43.906484+07', 'system_setup', '2025-06-14 16:22:05.673311+07', 'system_setup');
          INSERT INTO public.users VALUES (3, 'Gema', 'Drakel', 'gema.drakel', 'placeholder_hash', '<EMAIL>', false, false, NULL, NULL, NULL, '2025-06-12 16:54:43.906484+07', '2025-06-14 16:22:09.594751+07', NULL, 9, '2025-06-12 16:54:43.906484+07', 'system_setup', '2025-06-14 16:22:09.594751+07', 'system_setup');
          INSERT INTO public.users VALUES (2, 'Raja', 'Anugrah', 'raja.anugrah', 'placeholder_hash', '<EMAIL>', false, false, NULL, NULL, NULL, '2025-06-12 16:54:43.906484+07', '2025-06-14 16:22:13.078218+07', NULL, 9, '2025-06-12 16:54:43.906484+07', 'system_setup', '2025-06-14 16:22:13.078218+07', 'system_setup');
          INSERT INTO public.users VALUES (6, 'Test', 'User', 'testuser', '$2b$12$4JHQYlPObGL5K7awA6D3lexI0VdRCp3PXjNHAdjWu4SCLBRJZd2OW', '<EMAIL>', true, false, NULL, 0, 0, '2025-06-12 22:12:45.954151+07', '2025-06-16 13:30:30.879585+07', NULL, 9, '2025-06-12 22:12:45.954151+07', 'system', '2025-06-16 13:30:30.879585+07', 'system');
          INSERT INTO public.users VALUES (10, 'Load Test', 'User 1', 'loadtest_user_1', '$2b$12$aFIMHVH4n4o/Fr7yCYEB1eUhNtlLgFYojSWT3hzdy5QyBwjObVGrK', '<EMAIL>', true, false, NULL, 0, 0, '2025-06-13 22:26:08.699529+07', '2025-06-16 14:13:43.1164+07', NULL, 9, '2025-06-13 22:26:08.699529+07', NULL, '2025-06-16 14:13:43.1164+07', NULL);
          INSERT INTO public.users VALUES (8, 'Manager', 'User', 'manager', '$2b$12$T7vhKghWsYo6m6T2g0TlCOmJSjI.4PjIgLY5Ym3ekcA8nifJdidkS', '<EMAIL>', true, false, NULL, 0, 0, '2025-06-12 22:20:54.800473+07', '2025-06-16 14:14:15.549071+07', NULL, 9, '2025-06-12 22:20:54.800473+07', 'system', '2025-06-16 14:14:15.549071+07', 'system');
          INSERT INTO public.users VALUES (11, 'Load Test', 'User 2', 'loadtest_user_2', '$2b$12$63QJQxI66FiyazBLyod9yeGPxqqzRCeWjeEikwzMcnjah.1se4YSG', '<EMAIL>', true, false, NULL, 0, 0, '2025-06-13 22:26:09.001125+07', '2025-06-16 14:14:31.691028+07', NULL, 9, '2025-06-13 22:26:09.001125+07', NULL, '2025-06-16 14:14:31.691028+07', NULL);
          INSERT INTO public.users VALUES (12, 'Load Test', 'Admin', 'loadtest_admin', '$2b$12$Bz1YHCPLL8TovKjSyqOoqesbbDoOWrV2/lqi6zTiixAUG3JtyW74S', '<EMAIL>', true, true, NULL, 0, 0, '2025-06-13 22:26:09.244272+07', '2025-06-16 14:14:44.541673+07', NULL, 9, '2025-06-13 22:26:09.244272+07', NULL, '2025-06-16 14:14:44.541673+07', NULL);

          -- Insert data from user_roles table (4373.dat)
          INSERT INTO public.roles VALUES (1, 'Super Admin', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (2, 'Brand Admin', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (3, 'Data Architect', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (4, 'Data Editor', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (5, 'Data Entry', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (6, 'Viewer', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.roles VALUES (7, 'admin', '2025-06-12 22:12:45.887934+07', 'system', '2025-06-12 22:12:45.887934+07', 'system');
          INSERT INTO public.roles VALUES (8, 'user', '2025-06-12 22:12:45.887934+07', 'system', '2025-06-12 22:12:45.887934+07', 'system');
          INSERT INTO public.roles VALUES (9, 'manager', '2025-06-12 22:12:45.887934+07', 'system', '2025-06-12 22:12:45.887934+07', 'system');

          -- Insert data from user_brands table (4374.dat)
          INSERT INTO public.user_brands VALUES (1, 2, 1, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.user_brands VALUES (2, 3, 3, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.user_brands VALUES (3, 4, 2, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.user_brands VALUES (4, 5, 4, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');

          -- Insert data from data_inputs table (4375.dat)
          INSERT INTO public.data_inputs VALUES (1, 'Customer Information', 1, 'Basic customer information for Sozo Skin', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_inputs VALUES (2, 'Product Catalog', 1, 'Product information for Sozo Skin products', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_inputs VALUES (3, 'Patient Records', 2, 'Patient information for Sozo Dental clinics', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_inputs VALUES (4, 'Course Catalog', 3, 'Training courses offered by Sparks', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_inputs VALUES (5, 'Menu Items', 4, 'Food and beverage menu for Golden Lamian', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');

          -- Insert data from data_input_attributes table (4376.dat)
          INSERT INTO public.data_input_attributes VALUES (1, 1, 'First Name', 'Customer first name', 'Text', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (2, 1, 'Last Name', 'Customer last name', 'Text', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (3, 1, 'Email', 'Customer email address', 'Email', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (4, 1, 'Phone', 'Customer phone number', 'Text', false, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (5, 1, 'Date of Birth', 'Customer date of birth', 'Date', false, NULL, 'YYYY-MM-DD', '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (6, 2, 'Product Name', 'Name of the product', 'Text', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (7, 2, 'SKU', 'Product SKU code', 'Text', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (8, 2, 'Price', 'Product price', 'Decimal', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (9, 2, 'Category', 'Product category', 'Text', false, '["Skincare", "Cosmetics", "Tools", "Accessories"]', NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');
          INSERT INTO public.data_input_attributes VALUES (10, 3, 'Patient Name', 'Full name of the patient', 'Text', true, NULL, NULL, '2025-06-12 16:54:36.356977+07', 'system_setup', '2025-06-12 16:54:36.356977+07', 'system_setup');

          -- Set sequence values to match the data
          SELECT setval('roles_id_seq', 6, true);
          SELECT setval('brands_id_seq', 4, true);
          SELECT setval('users_id_seq', 5, true);
          SELECT setval('user_roles_id_seq', 5, true);
          SELECT setval('user_brands_id_seq', 4, true);
          SELECT setval('data_inputs_id_seq', 5, true);
          SELECT setval('data_input_attributes_id_seq', 10, true);
          EOF

          echo "Complete database migration with all 30 tables and data completed successfully!"
      containers:
      - name: backend
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        ports:
        - containerPort: 8000
        env:
        # Database Configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: DATABASE_URL
        # Security
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: sr-internal-service-secrets
              key: SECRET_KEY
        - name: ALGORITHM
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: ALGORITHM
        - name: ACCESS_TOKEN_EXPIRE_MINUTES
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: ACCESS_TOKEN_EXPIRE_MINUTES
        # API Configuration
        - name: API_V1_STR
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: API_V1_STR
        - name: PROJECT_NAME
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: PROJECT_NAME
        - name: PROJECT_VERSION
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: PROJECT_VERSION
        # CORS
        - name: BACKEND_CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: BACKEND_CORS_ORIGINS
        # File Storage
        - name: UPLOAD_DIR
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: UPLOAD_DIR
        - name: MAX_FILE_SIZE
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_FILE_SIZE
        - name: STORAGE_BACKEND
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: STORAGE_BACKEND
        # Image Processing
        - name: MAX_IMAGE_WIDTH
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_IMAGE_WIDTH
        - name: MAX_IMAGE_HEIGHT
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: MAX_IMAGE_HEIGHT
        - name: IMAGE_QUALITY
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: IMAGE_QUALITY
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: uploads-storage
          mountPath: /app/uploads
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: uploads-storage
        emptyDir: {}
      imagePullSecrets:
      - name: ghcr-secret

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sr-internal-service
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
