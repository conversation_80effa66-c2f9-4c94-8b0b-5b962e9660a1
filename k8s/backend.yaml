apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: sr-internal-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      initContainers:
      - name: db-migration
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        env:
        - name: DATABASE_URL
          value: "********************************************************/sr_internal_service"
        - name: PGPASSWORD
          value: "sr_password"
        command:
        - sh
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until python -c "import psycopg2; psycopg2.connect(host='postgresql-service', port=5432, user='sr_user', password='sr_password', database='sr_internal_service').close()"; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          echo "PostgreSQL is ready!"

          echo "Checking if database is already initialized..."
          USER_COUNT=$(python -c "import psycopg2; conn = psycopg2.connect(host='postgresql-service', port=5432, user='sr_user', password='sr_password', database='sr_internal_service'); cur = conn.cursor(); cur.execute('SELECT COUNT(*) FROM users;'); count = cur.fetchone()[0]; conn.close(); print(count)" 2>/dev/null || echo "0")

          if [ "$USER_COUNT" -gt "0" ]; then
            echo "Database already initialized with $USER_COUNT users"
            exit 0
          fi

          echo "Running Alembic migrations..."
          cd /app
          
          # Initialize alembic if no migrations exist
          if [ ! -d "alembic/versions" ] || [ -z "$(ls -A alembic/versions 2>/dev/null)" ]; then
            echo "No migrations found. Creating initial migration..."
            alembic revision --autogenerate -m "Initial migration"
          fi
          
          # Run migrations
          alembic upgrade head
          
          # Create superadmin user
          echo "Creating superadmin user..."
          python scripts/create_admin.py
          
          echo "Database migration completed successfully!"
      containers:
      - name: backend
        image: ghcr.io/seven-retail-group/sr-internal-service-backend:main
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "********************************************************/sr_internal_service"
        - name: SECRET_KEY
          value: "your-secret-key-here"
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: sr-internal-service
spec:
  selector:
    app: backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
