#!/usr/bin/env python3
import sys
sys.path.append('/app')
from sqlalchemy.orm import Session
from app.core.database import SessionLocal, engine
from app.core.security import get_password_hash
from app.models import User, Role, Brand
from app.core.database import Base

print('Creating database schema...')
Base.metadata.create_all(bind=engine)
print('✓ Database schema created')

db = SessionLocal()
try:
    # Create roles
    admin_role = db.query(Role).filter(Role.name == 'admin').first()
    if not admin_role:
        admin_role = Role(name='admin', created_by='system', updated_by='system')
        db.add(admin_role)
    
    user_role = db.query(Role).filter(Role.name == 'user').first()
    if not user_role:
        user_role = Role(name='user', created_by='system', updated_by='system')
        db.add(user_role)
    
    db.commit()
    print('✓ Roles created')
    
    # Create admin user
    admin_user = db.query(User).filter(User.username == 'admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            password=get_password_hash('admin123'),
            is_superuser=True,
            active=True,
            created_by='system',
            updated_by='system'
        )
        db.add(admin_user)
        db.commit()
        print('✓ Admin user created (username: admin, password: admin123)')
    else:
        print('✓ Admin user already exists')
    
    print('🎉 Database initialization completed!')
except Exception as e:
    print(f'❌ Error: {e}')
    db.rollback()
finally:
    db.close()
