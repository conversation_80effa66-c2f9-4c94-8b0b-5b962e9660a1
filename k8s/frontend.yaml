apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: sr-internal-service
  labels:
    app: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: ghcr.io/seven-retail-group/sr-internal-service-frontend:main
        ports:
        - containerPort: 80
        env:
        - name: VITE_API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: VITE_API_BASE_URL
        - name: VITE_APP_NAME
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: VITE_APP_NAME
        - name: VITE_APP_VERSION
          valueFrom:
            configMapKeyRef:
              name: sr-internal-service-config
              key: VITE_APP_VERSION
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      imagePullSecrets:
      - name: ghcr-secret

---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: sr-internal-service
  labels:
    app: frontend
spec:
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP
