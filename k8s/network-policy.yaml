apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: sr-internal-service-network-policy
  namespace: sr-internal-service
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from GKE Load Balancer
  - from:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000
  
  # Allow internal communication between services
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    - podSelector:
        matchLabels:
          app: backend
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 5432
  
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS outbound (for external APIs, package downloads, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  
  # Allow internal communication
  - to:
    - podSelector:
        matchLabels:
          app: frontend
    - podSelector:
        matchLabels:
          app: backend
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 5432
