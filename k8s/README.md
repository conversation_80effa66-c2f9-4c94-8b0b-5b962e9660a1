# SR Internal Service - Kubernetes Deployment on GKE

This directory contains Kubernetes manifests and deployment scripts for deploying the SR Internal Service to Google Kubernetes Engine (GKE).

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │    Ingress      │    │   Static IP     │
│      (GKE)      │────│   Controller    │────│   (Global)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                    ┌───────────┴───────────┐
                    │                       │
            ┌───────▼────────┐    ┌────────▼────────┐
            │   Frontend      │    │    Backend      │
            │   (Nginx)       │    │   (FastAPI)     │
            │   Port: 80      │    │   Port: 8000    │
            └────────────────┘    └─────────────────┘
                                           │
                                  ┌────────▼────────┐
                                  │   PostgreSQL    │
                                  │   Port: 5432    │
                                  │  (Persistent)   │
                                  └─────────────────┘
```

## 📁 File Structure

```
k8s/
├── namespace.yaml          # Kubernetes namespace
├── configmap.yaml         # Application configuration
├── secrets.yaml           # Sensitive data (passwords, keys)
├── postgresql.yaml        # PostgreSQL database deployment
├── backend.yaml           # Backend API deployment
├── frontend.yaml          # Frontend web deployment
├── ingress.yaml           # Load balancer and SSL configuration
├── hpa.yaml              # Horizontal Pod Autoscaler
├── rbac.yaml             # Role-based access control
├── network-policy.yaml   # Network security policies
├── monitoring.yaml       # Monitoring and alerting
├── deploy.sh            # Deployment script
└── README.md            # This file
```

## 🚀 Quick Start

### Prerequisites

1. **Google Cloud SDK** installed and configured
2. **kubectl** installed
3. **GKE cluster** created
4. **GitHub Personal Access Token** with package:read permissions

### 1. Create GKE Cluster (if not exists)

```bash
# Set your project ID
export PROJECT_ID="your-gcp-project-id"

# Create GKE cluster
gcloud container clusters create sr-internal-service-cluster \
    --project=$PROJECT_ID \
    --zone=us-central1-a \
    --machine-type=e2-standard-2 \
    --num-nodes=3 \
    --enable-autoscaling \
    --min-nodes=1 \
    --max-nodes=10 \
    --enable-autorepair \
    --enable-autoupgrade \
    --disk-size=50GB \
    --disk-type=pd-standard
```

### 2. Configure Environment Variables

```bash
export PROJECT_ID="your-gcp-project-id"
export CLUSTER_NAME="sr-internal-service-cluster"
export REGION="us-central1"
export GITHUB_TOKEN="your-github-personal-access-token"
```

### 3. Update Configuration Files

Before deploying, update the following files with your actual values:

#### `secrets.yaml`
```bash
# Encode your actual values
echo -n "********************************************************/sr_internal_service" | base64
echo -n "your-super-secret-key-change-this-in-production" | base64
echo -n "sr_internal_service" | base64
echo -n "sr_user" | base64
echo -n "sr_password" | base64
```

#### `configmap.yaml`
- Update `BACKEND_CORS_ORIGINS` with your frontend domain
- Update `VITE_API_BASE_URL` with your backend domain

#### `ingress.yaml`
- Replace `your-frontend-domain.com` with your actual frontend domain
- Replace `your-backend-domain.com` with your actual backend domain

### 4. Deploy to GKE

```bash
# Make the deployment script executable
chmod +x deploy.sh

# Run the deployment
./deploy.sh
```

## 🔧 Manual Deployment Steps

If you prefer to deploy manually:

### 1. Configure kubectl

```bash
gcloud container clusters get-credentials sr-internal-service-cluster \
    --region us-central1 \
    --project your-gcp-project-id
```

### 2. Create GitHub Container Registry Secret

```bash
kubectl create secret docker-registry ghcr-secret \
    --docker-server=ghcr.io \
    --docker-username=your-github-username \
    --docker-password=$GITHUB_TOKEN \
    --docker-email=<EMAIL> \
    --namespace=sr-internal-service
```

### 3. Create Static IP Address

```bash
gcloud compute addresses create sr-internal-service-ip \
    --global \
    --project=your-gcp-project-id
```

### 4. Deploy Resources in Order

```bash
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f configmap.yaml
kubectl apply -f rbac.yaml
kubectl apply -f postgresql.yaml

# Wait for PostgreSQL to be ready
kubectl wait --for=condition=ready pod -l app=postgresql -n sr-internal-service --timeout=300s

kubectl apply -f backend.yaml
kubectl apply -f frontend.yaml

# Wait for deployments to be ready
kubectl wait --for=condition=available deployment/backend -n sr-internal-service --timeout=300s
kubectl wait --for=condition=available deployment/frontend -n sr-internal-service --timeout=300s

kubectl apply -f ingress.yaml
kubectl apply -f hpa.yaml
kubectl apply -f network-policy.yaml
kubectl apply -f monitoring.yaml
```

## 🔍 Monitoring and Troubleshooting

### Check Deployment Status

```bash
# Check all resources
kubectl get all -n sr-internal-service

# Check pods
kubectl get pods -n sr-internal-service

# Check services
kubectl get services -n sr-internal-service

# Check ingress
kubectl get ingress -n sr-internal-service
```

### View Logs

```bash
# Backend logs
kubectl logs -f deployment/backend -n sr-internal-service

# Frontend logs
kubectl logs -f deployment/frontend -n sr-internal-service

# PostgreSQL logs
kubectl logs -f deployment/postgresql -n sr-internal-service
```

### Debug Pod Issues

```bash
# Describe pod
kubectl describe pod <pod-name> -n sr-internal-service

# Execute into pod
kubectl exec -it <pod-name> -n sr-internal-service -- /bin/bash

# Check events
kubectl get events -n sr-internal-service --sort-by='.lastTimestamp'
```

## 🔒 Security Features

- **Network Policies**: Restrict pod-to-pod communication
- **RBAC**: Role-based access control for service accounts
- **Secrets Management**: Sensitive data stored in Kubernetes secrets
- **SSL/TLS**: Managed certificates for HTTPS
- **Image Pull Secrets**: Secure access to private container registry

## 📊 Scaling and Performance

- **Horizontal Pod Autoscaler**: Automatically scales based on CPU/memory usage
- **Resource Limits**: Prevents resource exhaustion
- **Persistent Storage**: Database data persisted across pod restarts
- **Load Balancing**: Traffic distributed across multiple replicas

## 🔄 Updates and Maintenance

### Update Application Images

```bash
# Update backend image
kubectl set image deployment/backend backend=ghcr.io/seven-retail-group/sr-internal-service-backend:new-tag -n sr-internal-service

# Update frontend image
kubectl set image deployment/frontend frontend=ghcr.io/seven-retail-group/sr-internal-service-frontend:new-tag -n sr-internal-service
```

### Database Migrations

```bash
# Run database migrations
kubectl exec -it deployment/backend -n sr-internal-service -- alembic upgrade head
```

### Backup Database

```bash
# Create database backup
kubectl exec -it deployment/postgresql -n sr-internal-service -- pg_dump -U sr_user sr_internal_service > backup.sql
```

## 🌐 DNS Configuration

After deployment, configure your DNS to point to the static IP:

1. Get the static IP address:
   ```bash
   gcloud compute addresses describe sr-internal-service-ip --global --format="value(address)"
   ```

2. Create DNS A records:
   - `your-frontend-domain.com` → Static IP
   - `your-backend-domain.com` → Static IP

## 📞 Support

For issues and questions:
1. Check the logs using the commands above
2. Review the Kubernetes events
3. Verify all configuration values are correct
4. Ensure DNS is properly configured
