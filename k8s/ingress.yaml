apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sr-internal-service-ingress
  namespace: sr-internal-service
  annotations:
    # GKE Ingress annotations
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "sr-internal-service-ip"  # Create this static IP first
    networking.gke.io/managed-certificates: "sr-internal-service-ssl-cert"
    kubernetes.io/ingress.allow-http: "false"  # Redirect HTTP to HTTPS
    
    # Optional: Enable Cloud Armor for DDoS protection
    # cloud.google.com/armor-config: '{"your-security-policy": "your-policy-name"}'
    
    # Optional: Enable Cloud CDN
    # cloud.google.com/backend-config: '{"default": "sr-internal-service-backendconfig"}'
spec:
  rules:
  # Frontend domain
  - host: your-frontend-domain.com  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
  
  # Backend API domain
  - host: your-backend-domain.com  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 8000

---
# Managed SSL Certificate for HTTPS
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: sr-internal-service-ssl-cert
  namespace: sr-internal-service
spec:
  domains:
    - your-frontend-domain.com  # Replace with your actual domain
    - your-backend-domain.com   # Replace with your actual domain

---
# Optional: Backend configuration for advanced features
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: sr-internal-service-backendconfig
  namespace: sr-internal-service
spec:
  # Enable Cloud CDN
  cdn:
    enabled: true
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: false
  
  # Connection draining timeout
  connectionDraining:
    drainingTimeoutSec: 60
  
  # Session affinity (if needed)
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 3600
  
  # Health check configuration
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /api/v1/health
    port: 8000
