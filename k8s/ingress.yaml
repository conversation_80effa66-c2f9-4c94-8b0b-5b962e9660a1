apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sr-internal-service-ingress
  namespace: sr-internal-service
  annotations:
    # GKE Ingress annotations for staging (no DNS required)
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.allow-http: "true"  # Allow HTTP for staging

    # Backend configuration for path-based routing
    cloud.google.com/backend-config: '{"default": "sr-internal-service-backendconfig"}'
spec:
  rules:
  # Single rule for IP-based access with path routing
  - http:
      paths:
      # Backend API paths
      - path: /api/*
        pathType: ImplementationSpecific
        backend:
          service:
            name: backend-service
            port:
              number: 80
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 80
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 80
      - path: /redoc
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 80
      - path: /openapi.json
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 80
      # Frontend paths (default catch-all)
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80

---
# Optional: Backend configuration for advanced features
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: sr-internal-service-backendconfig
  namespace: sr-internal-service
spec:
  # Enable Cloud CDN
  cdn:
    enabled: true
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: false
  
  # Connection draining timeout
  connectionDraining:
    drainingTimeoutSec: 60
  
  # Session affinity (if needed)
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 3600
  
  # Health check configuration
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 80
