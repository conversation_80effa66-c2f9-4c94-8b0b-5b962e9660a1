#!/bin/bash

# SR Internal Service - Secret Encoding Helper
# This script helps encode secrets for Kubernetes deployment

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

encode_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local encoded_value=$(echo -n "$secret_value" | base64)
    
    echo -e "${GREEN}$secret_name:${NC} $encoded_value"
}

main() {
    print_info "SR Internal Service - Secret Encoding Helper"
    echo
    
    print_warning "Please provide the following values to encode for Kubernetes secrets:"
    echo
    
    # Database URL
    echo "For Kubernetes deployment, use: ********************************************************/sr_internal_service"
    read -p "Database URL [press enter for default]: " db_url
    if [ -z "$db_url" ]; then
        db_url="********************************************************/sr_internal_service"
    fi
    encode_secret "DATABASE_URL" "$db_url"
    
    # Secret Key
    read -p "Secret Key (for JWT tokens): " secret_key
    if [ ! -z "$secret_key" ]; then
        encode_secret "SECRET_KEY" "$secret_key"
    fi
    
    # Database Name
    read -p "Database Name [sr_internal_service]: " db_name
    if [ -z "$db_name" ]; then
        db_name="sr_internal_service"
    fi
    encode_secret "POSTGRES_DB" "$db_name"

    # Database User
    read -p "Database User [sr_user]: " db_user
    if [ -z "$db_user" ]; then
        db_user="sr_user"
    fi
    encode_secret "POSTGRES_USER" "$db_user"

    # Database Password
    read -s -p "Database Password [sr_password]: " db_password
    echo
    if [ -z "$db_password" ]; then
        db_password="sr_password"
    fi
    encode_secret "POSTGRES_PASSWORD" "$db_password"
    
    echo
    print_success "Secrets encoded successfully!"
    print_info "Copy the encoded values above and update them in k8s/secrets.yaml"
    
    echo
    print_warning "Example secrets.yaml format:"
    echo "data:"
    echo "  DATABASE_URL: <encoded_database_url>"
    echo "  SECRET_KEY: <encoded_secret_key>"
    echo "  POSTGRES_DB: <encoded_db_name>"
    echo "  POSTGRES_USER: <encoded_db_user>"
    echo "  POSTGRES_PASSWORD: <encoded_db_password>"
}

main "$@"
