apiVersion: v1
kind: ServiceAccount
metadata:
  name: sr-internal-service-sa
  namespace: sr-internal-service
  annotations:
    # Optional: Link to Google Service Account for Workload Identity
    # iam.gke.io/gcp-service-account: <EMAIL>

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: sr-internal-service
  name: sr-internal-service-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: sr-internal-service-rolebinding
  namespace: sr-internal-service
subjects:
- kind: ServiceAccount
  name: sr-internal-service-sa
  namespace: sr-internal-service
roleRef:
  kind: Role
  name: sr-internal-service-role
  apiGroup: rbac.authorization.k8s.io
