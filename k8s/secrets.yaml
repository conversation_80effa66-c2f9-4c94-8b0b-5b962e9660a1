apiVersion: v1
kind: Secret
metadata:
  name: sr-internal-service-secrets
  namespace: sr-internal-service
type: Opaque
data:
  # Database credentials (base64 encoded)
  # To encode: echo -n "your-value" | base64
  # Example values below - REPLACE WITH YOUR ACTUAL VALUES
  DATABASE_URL: ********************************************************************************************************
  SECRET_KEY: eW91ci1zdXBlci1zZWNyZXQta2V5LWNoYW5nZS10aGlzLWluLXByb2R1Y3Rpb24tcGxlYXNl
  
  # PostgreSQL Database Credentials
  POSTGRES_DB: c3JfaW50ZXJuYWxfc2VydmljZQ==  # sr_internal_service
  POSTGRES_USER: c3JfdXNlcg==  # sr_user
  POSTGRES_PASSWORD: c3JfcGFzc3dvcmQ=  # sr_password
  
  # Optional: AWS S3 credentials if using S3 storage
  # AWS_ACCESS_KEY_ID: ""
  # AWS_SECRET_ACCESS_KEY: ""
  # S3_BUCKET_NAME: ""

---
# Instructions for updating secrets:
# 1. Replace the base64 encoded values above with your actual credentials
# 2. To encode a value: echo -n "your-actual-value" | base64
# 3. Example:
#    echo -n "********************************************************/sr_internal_service" | base64
#    echo -n "your-super-secret-key-change-this-in-production-please" | base64
#    echo -n "sr_internal_service" | base64
#    echo -n "sr_user" | base64
#    echo -n "sr_password" | base64
