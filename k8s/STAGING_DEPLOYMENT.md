# 🚀 SR Internal Service - Staging Deployment Guide

## Overview
This is a **staging deployment** configuration that:
- ✅ Uses PostgreSQL 17
- ✅ Automatically migrates your local database structure
- ✅ Provides direct IP access (no DNS required)
- ✅ Includes database initialization and migration scripts

## Key Changes Made

### 1. PostgreSQL Version 17 ✅
- Updated to `postgres:17-alpine` in `postgresql.yaml`

### 2. Database Structure Migration ✅
- **Added init container** in `backend.yaml` that runs before the main backend
- **Automatic migration**: Runs your SQL migration scripts and alembic migrations
- **Database initialization**: Creates default users, roles, and brands
- **Migration scripts**: Added `migration-scripts.yaml` ConfigMap with your existing migrations

### 3. No DNS Required ✅
- **Direct IP access**: Application accessible via static IP address
- **Path-based routing**: Frontend at `/`, API at `/api/*`, docs at `/docs`
- **HTTP only**: No SSL certificates for staging
- **Auto-configuration**: <PERSON>ript automatically updates CORS and API URLs with static IP

## Quick Start

### 1. Set Environment Variables
```bash
export PROJECT_ID="your-gcp-project-id"
export CLUSTER_NAME="sr-internal-service-cluster"
export REGION="us-central1"
export GITHUB_TOKEN="your-github-personal-access-token"
```

### 2. Update Secrets (Optional)
```bash
cd k8s
./encode-secrets.sh
# Follow prompts or press enter for defaults:
# - Database: sr_internal_service
# - User: sr_user  
# - Password: sr_password
```

### 3. Deploy
```bash
./deploy.sh
```

### 4. Access Your Application
After deployment completes, you'll get:
```
🎉 Your application is available at:
  Frontend: http://YOUR_STATIC_IP
  Backend API: http://YOUR_STATIC_IP/api/v1
  API Documentation: http://YOUR_STATIC_IP/docs
  Alternative API Docs: http://YOUR_STATIC_IP/redoc
```

## What Happens During Deployment

### Database Migration Process
1. **PostgreSQL starts** with version 17
2. **Init container runs** before backend starts:
   - Installs PostgreSQL client tools
   - Waits for PostgreSQL to be ready
   - Runs SQL migration scripts from `/migrations/` directory
   - Runs alembic migrations
   - Executes database initialization script
3. **Backend starts** with fully migrated database

### Migration Scripts Included
- `add_soft_delete_columns.sql` - Adds soft delete functionality
- `add_brand_access_control.sql` - Adds brand-based access control
- `init_basic_data.sql` - Creates default brands and roles

### Network Configuration
- **Single static IP** for both frontend and backend
- **Path-based routing**:
  - `/` → Frontend (React app)
  - `/api/*` → Backend API
  - `/docs` → API documentation
  - `/redoc` → Alternative API docs
- **CORS automatically configured** with static IP

## Monitoring & Troubleshooting

### Check Deployment Status
```bash
kubectl get pods -n sr-internal-service
kubectl get services -n sr-internal-service
kubectl get ingress -n sr-internal-service
```

### View Logs
```bash
# Backend logs
kubectl logs -f deployment/backend -n sr-internal-service

# Database logs
kubectl logs -f deployment/postgresql -n sr-internal-service

# Migration logs (init container)
kubectl logs deployment/backend -c db-migration -n sr-internal-service
```

### Common Issues & Solutions

#### Database Connection Issues
```bash
# Check if PostgreSQL is running
kubectl get pods -l app=postgresql -n sr-internal-service

# Check database logs
kubectl logs -f deployment/postgresql -n sr-internal-service
```

#### Migration Issues
```bash
# Check migration logs
kubectl logs deployment/backend -c db-migration -n sr-internal-service

# Manually run migrations
kubectl exec -it deployment/backend -n sr-internal-service -- alembic upgrade head
```

#### Access Issues
```bash
# Get static IP
gcloud compute addresses describe sr-internal-service-ip --global --format="value(address)"

# Check ingress status
kubectl describe ingress sr-internal-service-ingress -n sr-internal-service
```

## File Structure
```
k8s/
├── postgresql.yaml          # PostgreSQL 17 deployment
├── backend.yaml            # Backend with init container for migrations
├── migration-scripts.yaml  # ConfigMap with your SQL migrations
├── ingress.yaml            # Path-based routing (no DNS)
├── configmap.yaml          # Auto-configured with static IP
├── secrets.yaml            # Database credentials
├── deploy.sh              # Automated deployment script
├── encode-secrets.sh      # Helper for encoding secrets
└── STAGING_DEPLOYMENT.md  # This file
```

## Next Steps After Deployment

1. **Test your application** at the provided IP address
2. **Verify database structure** by checking the API endpoints
3. **Test all functionality** including file uploads, data inputs, etc.
4. **Monitor resource usage** and adjust if needed
5. **For production**: Configure proper DNS and SSL certificates

## Cleanup
```bash
./cleanup.sh
```

This will remove all Kubernetes resources and the static IP address.

---

**Note**: This is a staging deployment optimized for testing. For production, you'll want to add proper DNS configuration, SSL certificates, and additional security measures.
