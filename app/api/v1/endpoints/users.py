from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.api import deps
from app.core.database import get_db
from app.crud import user as crud_user
from app.models.user import User
from app.schemas.user import User as UserSchema, UserCreate, UserUpdate

router = APIRouter()


def serialize_user(user) -> dict:
    """Convert User model to dict with role information"""
    role_name = None
    if user.user_roles:
        role_name = user.user_roles[0].role.name if user.user_roles[0].role else None

    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "active": user.active,
        "is_superuser": user.is_superuser,
        "role": role_name,
        "created_on": user.created_on.isoformat() if user.created_on else None,
        "changed_on": user.changed_on.isoformat() if user.changed_on else None,
        "last_login": user.last_login.isoformat() if user.last_login else None,
        "login_count": user.login_count,
        "fail_login_count": user.fail_login_count,
    }


@router.get("/")
def read_users(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    search: str = None,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Retrieve users with optional search.
    """
    users = crud_user.get_multi_with_roles(db, skip=skip, limit=limit, search=search)
    return [serialize_user(user) for user in users]


@router.post("/", response_model=UserSchema)
def create_user(
    *,
    db: Session = Depends(get_db),
    user_in: UserCreate,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Create new user.
    """
    user = crud_user.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="The user with this email already exists in the system.",
        )
    user = crud_user.get_by_username(db, username=user_in.username)
    if user:
        raise HTTPException(
            status_code=400,
            detail="The user with this username already exists in the system.",
        )
    user = crud_user.create(db, obj_in=user_in, created_by=current_user.username)
    return user


@router.put("/me", response_model=UserSchema)
def update_user_me(
    *,
    db: Session = Depends(get_db),
    password: str = None,
    first_name: str = None,
    last_name: str = None,
    email: str = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update own user.
    """
    current_user_data = {}
    if password is not None:
        current_user_data["password"] = password
    if first_name is not None:
        current_user_data["first_name"] = first_name
    if last_name is not None:
        current_user_data["last_name"] = last_name
    if email is not None:
        current_user_data["email"] = email
    user = crud_user.update(db, db_obj=current_user, obj_in=current_user_data, updated_by=current_user.username)
    return user


@router.get("/me", response_model=UserSchema)
def read_user_me(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get current user.
    """
    return current_user


@router.get("/{user_id}")
def read_user_by_id(
    user_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    user = crud_user.get_with_roles(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail="The user with this id does not exist in the system",
        )
    if user == current_user:
        return serialize_user(user)
    if not crud_user.is_superuser(current_user):
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return serialize_user(user)


@router.put("/{user_id}", response_model=UserSchema)
def update_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    Update a user.
    """
    user = crud_user.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail="The user with this id does not exist in the system",
        )
    user = crud_user.update(db, db_obj=user, obj_in=user_in, updated_by=current_user.username)
    return user
